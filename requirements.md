# 📸 Photographer <PERSON><PERSON><PERSON> App - Requirements

## 🎯 Purpose

<PERSON><PERSON><PERSON> a **React application** to showcase a photographer's portfolio specialised in:
- Pregnancy
- Newborns
- Follow-ups
- Baptisms
- Communions

## 📝 Features

### 1. Home Page
- Single-page hero section showcasing **highlighted photos**.
- Clean, elegant design inspired by attached images.

### 2. Navbar
- Menu items:
  - Home
  - Gallery
  - Contacts
  - Booking

### 3. Gallery Page
- Displays images by categories:
  - Pregnancy
  - Newborns
  - Follow-ups
  - Baptisms
  - Communions
- Images sourced from:
  - The photographer’s **Instagram account**.
  - **Database**, if images exist there.
- Always ordered by **most recent first**.

### 4. Proofing Gallery Page
- **Password-protected page**:
  - Password is defined in the Backoffice.
  - Backoffice generates a **unique URL** for each client to access their gallery.
- Upon correct password entry:
  - Displays selected or uploaded images for that client.
  - Allows clients to **select and download images**.

### 5. Contacts Page
- Displays:
  - Phone number
  - Email
  - Location (embedded Google Maps)
- Contact form with:
  - Name
  - Email
  - Message

### 6. Booking Page
- Shows **calendar** with available dates.
- Upon selecting a date:
  - Displays available time slots.
- Upon selecting a time slot:
  - Requests:
    - Name
    - Email
    - Phone number
- Bookings saved to Backoffice.

### 7. Backoffice (Admin Panel)
- Secure login with JWT authentication.
- Manage:
  - Photos in the gallery
  - Booking sessions (dates, times, client details)
  - Proofing galleries:
    - Upload/select images for each client.
    - Define password.
    - Generate **unique URL** for client proofing gallery access.

## 🔒 Security

- Admin panel with protected routes.
- Proofing gallery passwords hashed in DB.

## ⚙️ Non-Functional Requirements

- Page load time < 2s.
- Fully responsive for **mobile, tablet, and desktop**.
- SEO optimised for photography portfolio.

---
