# 📸 LiMarty Photography Portfolio

A modern, full-stack photography portfolio application specializing in pregnancy, newborn, and family photography.

## 🌟 Features

### Frontend (React + Vite)
- **Modern UI**: Clean, elegant design with Tailwind CSS
- **Responsive Design**: Mobile-first approach for all devices
- **Gallery System**: Categorized photo galleries with filtering
- **Booking System**: Interactive calendar with time slot selection
- **Contact Forms**: Professional contact and inquiry forms
- **Proofing Galleries**: Password-protected client galleries
- **Admin Dashboard**: Complete content management system

### Backend (Node.js + Express)
- **RESTful API**: Well-structured API endpoints
- **Authentication**: JWT-based admin authentication
- **File Upload**: Secure image upload and management
- **Database**: MongoDB with Mongoose ODM
- **Email Integration**: Contact form notifications
- **Security**: Rate limiting, CORS, and input validation

## 🚀 Quick Start

### Prerequisites
- Node.js (v16 or higher)
- MongoDB (local or Atlas)
- npm or yarn

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd LiMarty
   ```

2. **Backend Setup**
   ```bash
   cd backend
   npm install
   cp .env.example .env
   # Edit .env with your configuration
   npm run dev
   ```

3. **Frontend Setup**
   ```bash
   cd frontend
   npm install
   npm run dev
   ```

4. **Access the Application**
   - Frontend: http://localhost:5173
   - Backend API: http://localhost:5000
   - Admin Panel: http://localhost:5173/admin/login

### Default Admin Credentials
- Email: `<EMAIL>`
- Password: `admin123`

## 📁 Project Structure

```
LiMarty/
├── backend/
│   ├── config/          # Database configuration
│   ├── controllers/     # Route controllers
│   ├── middleware/      # Custom middleware
│   ├── models/          # MongoDB models
│   ├── routes/          # API routes
│   ├── uploads/         # File uploads
│   └── server.js        # Express server
├── frontend/
│   ├── src/
│   │   ├── components/  # Reusable components
│   │   ├── context/     # React context
│   │   ├── pages/       # Page components
│   │   └── App.jsx      # Main app component
│   └── public/          # Static assets
└── templatesPhotos/     # Design templates
```

## 🔧 Configuration

### Backend Environment Variables
```env
MONGODB_URI=mongodb://localhost:27017/limarty-photography
JWT_SECRET=your-super-secret-jwt-key
PORT=5000
NODE_ENV=development
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=admin123
FRONTEND_URL=http://localhost:5173
```

### Frontend Environment Variables
```env
VITE_API_URL=http://localhost:5000/api
```

## 📚 API Documentation

### Public Endpoints
- `GET /api/gallery` - Get public photos
- `GET /api/gallery/categories` - Get photo categories
- `GET /api/gallery/highlighted` - Get featured photos
- `POST /api/contact` - Submit contact form
- `POST /api/booking` - Create booking
- `GET /api/booking/availability/:date` - Check availability

### Proofing Gallery Endpoints
- `POST /api/proofing/login` - Authenticate to gallery
- `GET /api/proofing/:clientId` - Get gallery photos
- `POST /api/proofing/:clientId/select` - Toggle photo selection

### Admin Endpoints (Protected)
- `POST /api/admin/login` - Admin login
- `GET /api/admin/dashboard` - Dashboard statistics
- `GET /api/admin/photos` - Manage photos
- `POST /api/admin/photos` - Upload photos
- `GET /api/admin/bookings` - Manage bookings
- `GET /api/admin/contacts` - Manage contacts

## 🎨 Photography Categories

- **Pregnancy**: Maternity and pregnancy portraits
- **Newborns**: Newborn and baby photography
- **Follow-ups**: Milestone and growth sessions
- **Baptisms**: Religious ceremony photography
- **Communions**: First communion celebrations

## 🔐 Security Features

- JWT authentication for admin access
- Password hashing with bcrypt
- Rate limiting on sensitive endpoints
- CORS protection
- Input validation and sanitization
- Secure file upload handling

## 🚀 Deployment

### Backend Deployment
1. Set up MongoDB Atlas or your preferred database
2. Configure environment variables for production
3. Deploy to your preferred platform (Railway, Heroku, AWS, etc.)

### Frontend Deployment
1. Build the production version: `npm run build`
2. Deploy to Vercel, Netlify, or your preferred static hosting

## 🛠️ Development

### Adding New Features
1. Backend: Add routes in `/routes`, models in `/models`
2. Frontend: Add components in `/components`, pages in `/pages`
3. Update API documentation

### Database Models
- **Photo**: Gallery photos with categories and metadata
- **ProofingGallery**: Client galleries with password protection
- **Booking**: Session bookings with date/time slots
- **Contact**: Contact form submissions
- **Admin**: Admin user accounts

## 📝 License

This project is licensed under the ISC License.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Submit a pull request

## 📞 Support

For support or questions, please contact the development team or create an issue in the repository.

---

Built with ❤️ for LiMarty Photography
