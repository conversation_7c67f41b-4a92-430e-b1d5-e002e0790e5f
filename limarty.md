you want to build a new application
write 2 md documents with:
- requirements
- architecture

The app it's a photographer's portfolio and its on react
The main focus of the photographer is Pregnancy, Newborns, Follow-ups, Baptisms, Communions
The app has the main page is a single page with some of the photos
On the navbar it has home, gallery, contacts and booking
On the page of the gallery it has the categories and i want to get the images from there instagram account but also from the database if exists and always order by the most recent
On the page of Proofing gallery i want a protected page with a password defined on the backoffice and the images selected or uploaded on the backoffice
On the page of Proofing gallery the user after insert the correct password, it can select and download the images
On the page it shows the phone number, email, location, and a contact me page with the email of the client, name and message, and also a location on the maps
On the page of the booking, shows a calendar with the dates, after select date it show the times and selected the time it ask the user name, email and phone number
On the back office it can modifiy the photos on the galley, the booking sessons and the proofing gallery.