import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { AppProvider } from './context/AppContext';
import Navbar from './components/Navbar';
import HomePage from './pages/HomePage';
import GalleryPage from './pages/GalleryPage';
import ContactPage from './pages/ContactPage';
import BookingPage from './pages/BookingPage';
import ProofingGalleryPage from './pages/ProofingGalleryPage';
import AdminLogin from './pages/admin/AdminLogin';
import AdminDashboard from './pages/admin/AdminDashboard';
import ProtectedRoute from './components/ProtectedRoute';

function App() {
  return (
    <AppProvider>
      <Router>
        <div className="min-h-screen bg-white">
          <Routes>
            <Route path="/" element={<HomePage />} />
            <Route path="/gallery" element={<><Navbar /><GalleryPage /></>} />
            <Route path="/gallery/:category" element={<><Navbar /><GalleryPage /></>} />
            <Route path="/contact" element={<><Navbar /><ContactPage /></>} />
            <Route path="/booking" element={<><Navbar /><BookingPage /></>} />
            <Route path="/proofing-gallery/:clientId" element={<><Navbar /><ProofingGalleryPage /></>} />
            <Route path="/admin/login" element={<><Navbar /><AdminLogin /></>} />
            <Route
              path="/admin/*"
              element={
                <ProtectedRoute>
                  <><Navbar /><AdminDashboard /></>
                </ProtectedRoute>
              }
            />
          </Routes>
        </div>
      </Router>
    </AppProvider>
  );
}

export default App;
