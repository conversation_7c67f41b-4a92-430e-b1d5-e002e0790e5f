import { createContext, useContext, useReducer, useEffect } from 'react';
import axios from 'axios';

// Create context
const AppContext = createContext();

// API base URL
const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:5000/api';

// Configure axios defaults
axios.defaults.baseURL = API_BASE_URL;

// Initial state
const initialState = {
  // Auth state
  admin: null,
  isAuthenticated: false,
  accessToken: null,
  
  // Gallery state
  photos: [],
  categories: [],
  highlightedPhotos: [],
  
  // Booking state
  availableSlots: [],
  selectedDate: null,
  
  // Contact state
  contacts: [],
  
  // Proofing gallery state
  currentProofingGallery: null,
  selectedPhotos: [],
  
  // UI state
  loading: false,
  error: null,
  notification: null
};

// Action types
const actionTypes = {
  // Auth actions
  SET_AUTH: 'SET_AUTH',
  LOGOUT: 'LOGOUT',
  
  // Gallery actions
  SET_PHOTOS: 'SET_PHOTOS',
  SET_CATEGORIES: 'SET_CATEGORIES',
  SET_HIGHLIGHTED_PHOTOS: 'SET_HIGHLIGHTED_PHOTOS',
  
  // Booking actions
  SET_AVAILABLE_SLOTS: 'SET_AVAILABLE_SLOTS',
  SET_SELECTED_DATE: 'SET_SELECTED_DATE',
  
  // Contact actions
  SET_CONTACTS: 'SET_CONTACTS',
  
  // Proofing gallery actions
  SET_PROOFING_GALLERY: 'SET_PROOFING_GALLERY',
  SET_SELECTED_PHOTOS: 'SET_SELECTED_PHOTOS',
  TOGGLE_PHOTO_SELECTION: 'TOGGLE_PHOTO_SELECTION',
  
  // UI actions
  SET_LOADING: 'SET_LOADING',
  SET_ERROR: 'SET_ERROR',
  SET_NOTIFICATION: 'SET_NOTIFICATION',
  CLEAR_NOTIFICATION: 'CLEAR_NOTIFICATION'
};

// Reducer function
const appReducer = (state, action) => {
  switch (action.type) {
    case actionTypes.SET_AUTH:
      return {
        ...state,
        admin: action.payload.admin,
        isAuthenticated: action.payload.isAuthenticated,
        accessToken: action.payload.accessToken
      };
      
    case actionTypes.LOGOUT:
      localStorage.removeItem('accessToken');
      localStorage.removeItem('refreshToken');
      return {
        ...state,
        admin: null,
        isAuthenticated: false,
        accessToken: null
      };
      
    case actionTypes.SET_PHOTOS:
      return {
        ...state,
        photos: action.payload
      };
      
    case actionTypes.SET_CATEGORIES:
      return {
        ...state,
        categories: action.payload
      };
      
    case actionTypes.SET_HIGHLIGHTED_PHOTOS:
      return {
        ...state,
        highlightedPhotos: action.payload
      };
      
    case actionTypes.SET_AVAILABLE_SLOTS:
      return {
        ...state,
        availableSlots: action.payload
      };
      
    case actionTypes.SET_SELECTED_DATE:
      return {
        ...state,
        selectedDate: action.payload
      };
      
    case actionTypes.SET_CONTACTS:
      return {
        ...state,
        contacts: action.payload
      };
      
    case actionTypes.SET_PROOFING_GALLERY:
      return {
        ...state,
        currentProofingGallery: action.payload
      };
      
    case actionTypes.SET_SELECTED_PHOTOS:
      return {
        ...state,
        selectedPhotos: action.payload
      };
      
    case actionTypes.TOGGLE_PHOTO_SELECTION:
      const { photoId, selected } = action.payload;
      const updatedSelectedPhotos = selected
        ? [...state.selectedPhotos, photoId]
        : state.selectedPhotos.filter(id => id !== photoId);
      
      return {
        ...state,
        selectedPhotos: updatedSelectedPhotos
      };
      
    case actionTypes.SET_LOADING:
      return {
        ...state,
        loading: action.payload
      };
      
    case actionTypes.SET_ERROR:
      return {
        ...state,
        error: action.payload,
        loading: false
      };
      
    case actionTypes.SET_NOTIFICATION:
      return {
        ...state,
        notification: action.payload
      };
      
    case actionTypes.CLEAR_NOTIFICATION:
      return {
        ...state,
        notification: null
      };
      
    default:
      return state;
  }
};

// Provider component
export const AppProvider = ({ children }) => {
  const [state, dispatch] = useReducer(appReducer, initialState);
  
  // Setup axios interceptors
  useEffect(() => {
    // Request interceptor to add auth token
    const requestInterceptor = axios.interceptors.request.use(
      (config) => {
        if (state.accessToken) {
          config.headers.Authorization = `Bearer ${state.accessToken}`;
        }
        return config;
      },
      (error) => Promise.reject(error)
    );
    
    // Response interceptor to handle auth errors
    const responseInterceptor = axios.interceptors.response.use(
      (response) => response,
      async (error) => {
        if (error.response?.status === 401 && state.isAuthenticated) {
          // Try to refresh token
          try {
            const refreshToken = localStorage.getItem('refreshToken');
            if (refreshToken) {
              const response = await axios.post('/admin/refresh', { refreshToken });
              const { accessToken } = response.data;
              
              dispatch({
                type: actionTypes.SET_AUTH,
                payload: {
                  ...state,
                  accessToken,
                  isAuthenticated: true
                }
              });
              
              localStorage.setItem('accessToken', accessToken);
              
              // Retry the original request
              error.config.headers.Authorization = `Bearer ${accessToken}`;
              return axios.request(error.config);
            }
          } catch (refreshError) {
            // Refresh failed, logout user
            dispatch({ type: actionTypes.LOGOUT });
          }
        }
        return Promise.reject(error);
      }
    );
    
    // Cleanup interceptors
    return () => {
      axios.interceptors.request.eject(requestInterceptor);
      axios.interceptors.response.eject(responseInterceptor);
    };
  }, [state.accessToken, state.isAuthenticated]);
  
  // Check for stored auth token on app load
  useEffect(() => {
    const accessToken = localStorage.getItem('accessToken');
    if (accessToken) {
      dispatch({
        type: actionTypes.SET_AUTH,
        payload: {
          admin: null, // Will be fetched from API
          isAuthenticated: true,
          accessToken
        }
      });
    }
  }, []);
  
  // Auto-clear notifications after 5 seconds
  useEffect(() => {
    if (state.notification) {
      const timer = setTimeout(() => {
        dispatch({ type: actionTypes.CLEAR_NOTIFICATION });
      }, 5000);
      
      return () => clearTimeout(timer);
    }
  }, [state.notification]);
  
  const value = {
    state,
    dispatch,
    actionTypes
  };
  
  return (
    <AppContext.Provider value={value}>
      {children}
    </AppContext.Provider>
  );
};

// Custom hook to use the context
export const useApp = () => {
  const context = useContext(AppContext);
  if (!context) {
    throw new Error('useApp must be used within an AppProvider');
  }
  return context;
};
