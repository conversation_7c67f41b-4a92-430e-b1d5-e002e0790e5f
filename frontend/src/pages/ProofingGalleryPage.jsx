import { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { useApp } from '../context/AppContext';
import { Lock, Eye, Download, Check, X } from 'lucide-react';
import axios from 'axios';

const ProofingGalleryPage = () => {
  const { clientId } = useParams();
  const { dispatch, actionTypes } = useApp();
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [password, setPassword] = useState('');
  const [gallery, setGallery] = useState(null);
  const [photos, setPhotos] = useState([]);
  const [selectedPhotos, setSelectedPhotos] = useState([]);
  const [loading, setLoading] = useState(false);
  const [authError, setAuthError] = useState('');

  const handlePasswordSubmit = async (e) => {
    e.preventDefault();
    
    if (!password.trim()) {
      setAuthError('Please enter the password');
      return;
    }

    try {
      setLoading(true);
      setAuthError('');
      
      const response = await axios.post('/proofing/login', {
        clientId,
        password: password.trim()
      });

      if (response.data.success) {
        setIsAuthenticated(true);
        setGallery(response.data.gallery);
        await fetchGalleryPhotos();
      }
    } catch (error) {
      console.error('Authentication error:', error);
      setAuthError(error.response?.data?.error || 'Invalid password');
    } finally {
      setLoading(false);
    }
  };

  const fetchGalleryPhotos = async () => {
    try {
      const response = await axios.get(`/proofing/${clientId}`);
      setPhotos(response.data.photos);
      setGallery(response.data.gallery);
      
      // Set initially selected photos
      const initiallySelected = response.data.photos
        .filter(photo => photo.isSelected)
        .map(photo => photo.id);
      setSelectedPhotos(initiallySelected);
    } catch (error) {
      console.error('Error fetching gallery photos:', error);
      dispatch({
        type: actionTypes.SET_ERROR,
        payload: 'Failed to load gallery photos'
      });
    }
  };

  const togglePhotoSelection = async (photoId) => {
    const isCurrentlySelected = selectedPhotos.includes(photoId);
    const newSelectionState = !isCurrentlySelected;

    // Check selection limits
    if (newSelectionState && gallery?.settings?.maxSelections) {
      if (selectedPhotos.length >= gallery.settings.maxSelections) {
        dispatch({
          type: actionTypes.SET_ERROR,
          payload: `Maximum ${gallery.settings.maxSelections} photos can be selected`
        });
        return;
      }
    }

    try {
      await axios.post(`/proofing/${clientId}/select`, {
        photoId,
        selected: newSelectionState
      });

      // Update local state
      if (newSelectionState) {
        setSelectedPhotos(prev => [...prev, photoId]);
      } else {
        setSelectedPhotos(prev => prev.filter(id => id !== photoId));
      }

      // Update photo in photos array
      setPhotos(prev => prev.map(photo => 
        photo.id === photoId 
          ? { ...photo, isSelected: newSelectionState, selectedAt: newSelectionState ? new Date() : null }
          : photo
      ));
    } catch (error) {
      console.error('Error toggling photo selection:', error);
      dispatch({
        type: actionTypes.SET_ERROR,
        payload: error.response?.data?.error || 'Failed to update photo selection'
      });
    }
  };

  const downloadSelectedPhotos = async () => {
    if (selectedPhotos.length === 0) {
      dispatch({
        type: actionTypes.SET_ERROR,
        payload: 'Please select photos to download'
      });
      return;
    }

    try {
      const response = await axios.get(`/proofing/${clientId}/selected`);
      const selectedPhotoData = response.data.selectedPhotos;

      // Create download links for each photo
      selectedPhotoData.forEach((photo) => {
        const link = document.createElement('a');
        link.href = photo.url;
        link.download = photo.originalName || photo.filename;
        link.target = '_blank';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      });

      dispatch({
        type: actionTypes.SET_NOTIFICATION,
        payload: {
          type: 'success',
          message: `Downloading ${selectedPhotoData.length} selected photos`
        }
      });
    } catch (error) {
      console.error('Error downloading photos:', error);
      dispatch({
        type: actionTypes.SET_ERROR,
        payload: 'Failed to download photos'
      });
    }
  };

  if (!isAuthenticated) {
    return (
      <div className="min-h-screen bg-neutral-50 flex items-center justify-center">
        <div className="max-w-md w-full mx-4">
          <div className="bg-white rounded-lg shadow-md p-8">
            <div className="text-center mb-6">
              <Lock className="h-12 w-12 text-primary-600 mx-auto mb-4" />
              <h1 className="text-2xl font-serif font-bold text-neutral-900 mb-2">
                Private Gallery
              </h1>
              <p className="text-neutral-600">
                Enter the password to access your photos
              </p>
            </div>

            <form onSubmit={handlePasswordSubmit}>
              <div className="mb-4">
                <label htmlFor="password" className="block text-sm font-medium text-neutral-700 mb-2">
                  Password
                </label>
                <input
                  type="password"
                  id="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  className="input-field"
                  placeholder="Enter gallery password"
                  autoFocus
                />
                {authError && (
                  <p className="text-red-600 text-sm mt-2">{authError}</p>
                )}
              </div>

              <button
                type="submit"
                disabled={loading}
                className="btn-primary w-full disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {loading ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                    Verifying...
                  </>
                ) : (
                  <>
                    <Eye className="mr-2 h-4 w-4" />
                    Access Gallery
                  </>
                )}
              </button>
            </form>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-white">
      {/* Header */}
      <div className="bg-neutral-50 border-b border-neutral-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
            <div>
              <h1 className="text-3xl md:text-4xl font-serif font-bold text-neutral-900 mb-2">
                {gallery?.title || 'Your Gallery'}
              </h1>
              {gallery?.description && (
                <p className="text-lg text-neutral-600">{gallery.description}</p>
              )}
            </div>
            
            {gallery?.settings?.allowDownload && (
              <div className="flex items-center space-x-4">
                <span className="text-sm text-neutral-600">
                  {selectedPhotos.length} selected
                  {gallery?.settings?.maxSelections && (
                    <span> of {gallery.settings.maxSelections} max</span>
                  )}
                </span>
                <button
                  onClick={downloadSelectedPhotos}
                  disabled={selectedPhotos.length === 0}
                  className="btn-primary flex items-center disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <Download className="mr-2 h-4 w-4" />
                  Download Selected
                </button>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Photo Grid */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {photos.length > 0 ? (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {photos.map((photo) => (
              <div
                key={photo.id}
                className="group relative aspect-square overflow-hidden rounded-lg shadow-md hover:shadow-xl transition-shadow duration-300"
              >
                <img
                  src={photo.url}
                  alt={photo.title}
                  className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                />
                
                {/* Selection Overlay */}
                <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-opacity duration-300">
                  <div className="absolute top-4 right-4">
                    <button
                      onClick={() => togglePhotoSelection(photo.id)}
                      className={`w-8 h-8 rounded-full border-2 flex items-center justify-center transition-colors duration-200 ${
                        selectedPhotos.includes(photo.id)
                          ? 'bg-primary-600 border-primary-600 text-white'
                          : 'bg-white border-white text-neutral-600 hover:bg-primary-600 hover:border-primary-600 hover:text-white'
                      }`}
                    >
                      {selectedPhotos.includes(photo.id) ? (
                        <Check className="h-4 w-4" />
                      ) : (
                        <div className="w-3 h-3 rounded-full border border-current" />
                      )}
                    </button>
                  </div>
                </div>

                {/* Photo Info */}
                <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black to-transparent p-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                  <h3 className="text-white font-medium">{photo.title}</h3>
                  {photo.isSelected && (
                    <p className="text-primary-200 text-sm">Selected</p>
                  )}
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <p className="text-lg text-neutral-600">
              No photos available in this gallery.
            </p>
          </div>
        )}
      </div>

      {/* Gallery Info */}
      {gallery && (
        <div className="bg-neutral-50 border-t border-neutral-200">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <div className="text-center text-sm text-neutral-600">
              <p>
                Gallery expires on {new Date(gallery.expiresAt).toLocaleDateString()}
              </p>
              {gallery.settings?.maxSelections && (
                <p className="mt-1">
                  Maximum {gallery.settings.maxSelections} photos can be selected
                </p>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ProofingGalleryPage;
