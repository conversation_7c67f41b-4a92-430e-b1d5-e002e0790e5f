import { useState, useEffect } from 'react';
import { use<PERSON>ara<PERSON>, Link } from 'react-router-dom';
import { useApp } from '../context/AppContext';
import { Grid, List, Filter, ChevronDown } from 'lucide-react';
import axios from 'axios';

const GalleryPage = () => {
  const { category } = useParams();
  const { state, dispatch, actionTypes } = useApp();
  const [photos, setPhotos] = useState([]);
  const [categories, setCategories] = useState([]);
  const [loading, setLoading] = useState(true);
  const [viewMode, setViewMode] = useState('grid');
  const [selectedCategory, setSelectedCategory] = useState(category || 'all');
  const [showCategoryFilter, setShowCategoryFilter] = useState(false);
  const [pagination, setPagination] = useState({
    current: 1,
    total: 1,
    count: 0,
    totalPhotos: 0
  });

  const categoryDisplayNames = {
    'all': 'All Photos',
    'pregnancy': 'Pregnancy',
    'newborns': 'Newborns',
    'follow-ups': 'Follow-ups',
    'baptisms': 'Baptisms',
    'communions': 'Communions'
  };

  useEffect(() => {
    const fetchCategories = async () => {
      try {
        const response = await axios.get('/gallery/categories');
        setCategories([
          { name: 'all', displayName: 'All Photos', count: 0 },
          ...response.data.categories
        ]);
      } catch (error) {
        console.error('Error fetching categories:', error);
      }
    };

    fetchCategories();
  }, []);

  useEffect(() => {
    const fetchPhotos = async () => {
      try {
        setLoading(true);
        const endpoint = selectedCategory === 'all' 
          ? '/gallery'
          : `/gallery/category/${selectedCategory}`;
        
        const response = await axios.get(`${endpoint}?limit=20&page=1`);
        setPhotos(response.data.photos);
        setPagination(response.data.pagination);
      } catch (error) {
        console.error('Error fetching photos:', error);
        dispatch({
          type: actionTypes.SET_ERROR,
          payload: 'Failed to load gallery photos'
        });
      } finally {
        setLoading(false);
      }
    };

    fetchPhotos();
  }, [selectedCategory, dispatch, actionTypes]);

  useEffect(() => {
    if (category && category !== selectedCategory) {
      setSelectedCategory(category);
    }
  }, [category]);

  const handleCategoryChange = (newCategory) => {
    setSelectedCategory(newCategory);
    setShowCategoryFilter(false);
  };

  const loadMorePhotos = async () => {
    if (pagination.current >= pagination.total) return;

    try {
      const endpoint = selectedCategory === 'all' 
        ? '/gallery'
        : `/gallery/category/${selectedCategory}`;
      
      const response = await axios.get(`${endpoint}?limit=20&page=${pagination.current + 1}`);
      setPhotos(prev => [...prev, ...response.data.photos]);
      setPagination(response.data.pagination);
    } catch (error) {
      console.error('Error loading more photos:', error);
    }
  };

  return (
    <div className="min-h-screen bg-white">
      {/* Header */}
      <div className="bg-neutral-50 border-b border-neutral-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <h1 className="text-3xl md:text-4xl font-serif font-bold text-neutral-900 mb-4">
            {categoryDisplayNames[selectedCategory] || 'Gallery'}
          </h1>
          <p className="text-lg text-neutral-600">
            {selectedCategory === 'all' 
              ? 'Explore our complete collection of photography work'
              : `Beautiful ${categoryDisplayNames[selectedCategory].toLowerCase()} photography`
            }
          </p>
        </div>
      </div>

      {/* Controls */}
      <div className="bg-white border-b border-neutral-200 sticky top-16 z-40">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
            {/* Category Filter */}
            <div className="relative">
              <button
                onClick={() => setShowCategoryFilter(!showCategoryFilter)}
                className="flex items-center space-x-2 px-4 py-2 border border-neutral-300 rounded-lg hover:bg-neutral-50 transition-colors duration-200"
              >
                <Filter className="h-4 w-4" />
                <span>{categoryDisplayNames[selectedCategory]}</span>
                <ChevronDown className="h-4 w-4" />
              </button>
              
              {showCategoryFilter && (
                <div className="absolute top-full left-0 mt-1 w-48 bg-white border border-neutral-200 rounded-lg shadow-lg z-50">
                  {categories.map((cat) => (
                    <button
                      key={cat.name}
                      onClick={() => handleCategoryChange(cat.name)}
                      className={`w-full text-left px-4 py-2 hover:bg-neutral-50 transition-colors duration-200 ${
                        selectedCategory === cat.name ? 'bg-primary-50 text-primary-600' : ''
                      }`}
                    >
                      <div className="flex justify-between items-center">
                        <span>{cat.displayName}</span>
                        {cat.count > 0 && (
                          <span className="text-sm text-neutral-500">({cat.count})</span>
                        )}
                      </div>
                    </button>
                  ))}
                </div>
              )}
            </div>

            {/* View Mode Toggle */}
            <div className="flex items-center space-x-2">
              <button
                onClick={() => setViewMode('grid')}
                className={`p-2 rounded-lg transition-colors duration-200 ${
                  viewMode === 'grid' 
                    ? 'bg-primary-100 text-primary-600' 
                    : 'text-neutral-600 hover:bg-neutral-100'
                }`}
              >
                <Grid className="h-5 w-5" />
              </button>
              <button
                onClick={() => setViewMode('list')}
                className={`p-2 rounded-lg transition-colors duration-200 ${
                  viewMode === 'list' 
                    ? 'bg-primary-100 text-primary-600' 
                    : 'text-neutral-600 hover:bg-neutral-100'
                }`}
              >
                <List className="h-5 w-5" />
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Photo Grid */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {loading ? (
          <div className={`grid gap-6 ${
            viewMode === 'grid' 
              ? 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4' 
              : 'grid-cols-1'
          }`}>
            {[...Array(12)].map((_, index) => (
              <div 
                key={index} 
                className={`bg-neutral-200 rounded-lg animate-pulse ${
                  viewMode === 'grid' ? 'aspect-square' : 'h-64'
                }`} 
              />
            ))}
          </div>
        ) : photos.length > 0 ? (
          <>
            <div className={`grid gap-6 ${
              viewMode === 'grid' 
                ? 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4' 
                : 'grid-cols-1'
            }`}>
              {photos.map((photo) => (
                <div
                  key={photo._id}
                  className={`group relative overflow-hidden rounded-lg shadow-md hover:shadow-xl transition-shadow duration-300 ${
                    viewMode === 'grid' ? 'aspect-square' : 'aspect-video'
                  }`}
                >
                  <img
                    src={photo.fullUrl || photo.url}
                    alt={photo.title}
                    className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                  <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-40 transition-opacity duration-300 flex items-end">
                    <div className="p-4 text-white opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                      <h3 className="font-medium text-lg">{photo.title}</h3>
                      <p className="text-sm capitalize">{photo.category.replace('-', ' ')}</p>
                      {photo.description && (
                        <p className="text-sm mt-1 opacity-90">{photo.description}</p>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* Load More Button */}
            {pagination.current < pagination.total && (
              <div className="text-center mt-12">
                <button
                  onClick={loadMorePhotos}
                  className="btn-primary px-8 py-3"
                >
                  Load More Photos
                </button>
              </div>
            )}

            {/* Photo Count */}
            <div className="text-center mt-8 text-neutral-600">
              Showing {photos.length} of {pagination.totalPhotos} photos
            </div>
          </>
        ) : (
          <div className="text-center py-12">
            <p className="text-lg text-neutral-600">
              No photos found in this category.
            </p>
            <Link
              to="/gallery"
              className="btn-primary mt-4 inline-block"
            >
              View All Photos
            </Link>
          </div>
        )}
      </div>
    </div>
  );
};

export default GalleryPage;
