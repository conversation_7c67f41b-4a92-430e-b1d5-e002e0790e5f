import { useState, useEffect } from 'react';
import { useApp } from '../context/AppContext';
import { Calendar, Clock, User, Mail, Phone, Check } from 'lucide-react';
import ReactCalendar from 'react-calendar';
import axios from 'axios';
import 'react-calendar/dist/Calendar.css';

const BookingPage = () => {
  const { dispatch, actionTypes } = useApp();
  const [selectedDate, setSelectedDate] = useState(null);
  const [availableSlots, setAvailableSlots] = useState([]);
  const [selectedSlot, setSelectedSlot] = useState(null);
  const [loadingSlots, setLoadingSlots] = useState(false);
  const [step, setStep] = useState(1); // 1: Date, 2: Time, 3: Details, 4: Confirmation
  const [formData, setFormData] = useState({
    clientName: '',
    clientEmail: '',
    clientPhone: '',
    sessionType: 'pregnancy',
    location: 'studio',
    notes: ''
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [bookingConfirmed, setBookingConfirmed] = useState(false);

  const sessionTypes = [
    { value: 'pregnancy', label: 'Pregnancy Session' },
    { value: 'newborn', label: 'Newborn Session' },
    { value: 'follow-up', label: 'Follow-up Session' },
    { value: 'baptism', label: 'Baptism Photography' },
    { value: 'communion', label: 'Communion Photography' },
    { value: 'other', label: 'Other' }
  ];

  const locations = [
    { value: 'studio', label: 'Studio' },
    { value: 'outdoor', label: 'Outdoor Location' },
    { value: 'client-home', label: 'Client Home' },
    { value: 'venue', label: 'Event Venue' }
  ];

  useEffect(() => {
    if (selectedDate) {
      fetchAvailableSlots(selectedDate);
    }
  }, [selectedDate]);

  const fetchAvailableSlots = async (date) => {
    try {
      setLoadingSlots(true);
      const dateString = date.toISOString().split('T')[0];
      const response = await axios.get(`/booking/availability/${dateString}`);
      setAvailableSlots(response.data.availableSlots);
    } catch (error) {
      console.error('Error fetching available slots:', error);
      dispatch({
        type: actionTypes.SET_ERROR,
        payload: 'Failed to load available time slots'
      });
    } finally {
      setLoadingSlots(false);
    }
  };

  const handleDateChange = (date) => {
    setSelectedDate(date);
    setSelectedSlot(null);
    setStep(2);
  };

  const handleSlotSelect = (slot) => {
    setSelectedSlot(slot);
    setStep(3);
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!formData.clientName || !formData.clientEmail || !formData.clientPhone) {
      dispatch({
        type: actionTypes.SET_ERROR,
        payload: 'Please fill in all required fields'
      });
      return;
    }

    try {
      setIsSubmitting(true);

      const bookingData = {
        ...formData,
        date: selectedDate.toISOString(),
        timeSlot: selectedSlot
      };

      await axios.post('/booking', bookingData);

      setBookingConfirmed(true);
      setStep(4);

      dispatch({
        type: actionTypes.SET_NOTIFICATION,
        payload: {
          type: 'success',
          message: 'Booking request submitted successfully!'
        }
      });
    } catch (error) {
      console.error('Booking error:', error);
      dispatch({
        type: actionTypes.SET_ERROR,
        payload: error.response?.data?.error || 'Failed to submit booking. Please try again.'
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const resetBooking = () => {
    setSelectedDate(null);
    setSelectedSlot(null);
    setStep(1);
    setFormData({
      clientName: '',
      clientEmail: '',
      clientPhone: '',
      sessionType: 'pregnancy',
      location: 'studio',
      notes: ''
    });
    setBookingConfirmed(false);
  };

  // Disable past dates and Sundays
  const tileDisabled = ({ date, view }) => {
    if (view === 'month') {
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      return date < today || date.getDay() === 0; // Disable past dates and Sundays
    }
    return false;
  };

  return (
    <div className="min-h-screen bg-white">
      {/* Header */}
      <div className="bg-neutral-50 border-b border-neutral-200">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="text-center">
            <h1 className="text-3xl md:text-4xl font-serif font-bold text-neutral-900 mb-4">
              Book Your Session
            </h1>
            <p className="text-lg text-neutral-600">
              Schedule your photography session in just a few simple steps
            </p>
          </div>
        </div>
      </div>

      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Progress Steps */}
        <div className="flex items-center justify-center mb-12">
          {[1, 2, 3, 4].map((stepNumber) => (
            <div key={stepNumber} className="flex items-center">
              <div className={`flex items-center justify-center w-10 h-10 rounded-full border-2 ${step >= stepNumber
                  ? 'bg-primary-600 border-primary-600 text-white'
                  : 'border-neutral-300 text-neutral-400'
                }`}>
                {step > stepNumber ? (
                  <Check className="h-5 w-5" />
                ) : (
                  stepNumber
                )}
              </div>
              {stepNumber < 4 && (
                <div className={`w-16 h-0.5 mx-2 ${step > stepNumber ? 'bg-primary-600' : 'bg-neutral-300'
                  }`} />
              )}
            </div>
          ))}
        </div>

        {/* Step Content */}
        {step === 1 && (
          <div className="text-center">
            <h2 className="text-2xl font-serif font-semibold text-neutral-900 mb-6">
              Select a Date
            </h2>
            <div className="inline-block">
              <ReactCalendar
                onChange={handleDateChange}
                value={selectedDate}
                tileDisabled={tileDisabled}
                className="border border-neutral-300 rounded-lg p-4"
                minDate={new Date()}
              />
            </div>
            <p className="text-sm text-neutral-600 mt-4">
              Sessions are not available on Sundays
            </p>
          </div>
        )}

        {step === 2 && (
          <div className="text-center">
            <h2 className="text-2xl font-serif font-semibold text-neutral-900 mb-6">
              Choose a Time
            </h2>
            <p className="text-lg text-neutral-600 mb-8">
              {selectedDate?.toLocaleDateString('en-US', {
                weekday: 'long',
                year: 'numeric',
                month: 'long',
                day: 'numeric'
              })}
            </p>

            {loadingSlots ? (
              <div className="flex justify-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600" />
              </div>
            ) : availableSlots.length > 0 ? (
              <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 gap-4 max-w-2xl mx-auto">
                {availableSlots.map((slot) => (
                  <button
                    key={slot}
                    onClick={() => handleSlotSelect(slot)}
                    className="p-4 border border-neutral-300 rounded-lg hover:border-primary-600 hover:bg-primary-50 transition-colors duration-200"
                  >
                    <Clock className="h-5 w-5 mx-auto mb-2 text-primary-600" />
                    <span className="font-medium">{slot}</span>
                  </button>
                ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <p className="text-lg text-neutral-600 mb-4">
                  No available time slots for this date
                </p>
                <button
                  onClick={() => setStep(1)}
                  className="btn-secondary"
                >
                  Choose Different Date
                </button>
              </div>
            )}
          </div>
        )}

        {step === 3 && (
          <div>
            <h2 className="text-2xl font-serif font-semibold text-neutral-900 mb-6 text-center">
              Your Details
            </h2>

            <div className="bg-neutral-50 rounded-lg p-6 mb-8">
              <h3 className="font-medium text-neutral-900 mb-2">Selected Appointment</h3>
              <p className="text-neutral-600">
                {selectedDate?.toLocaleDateString('en-US', {
                  weekday: 'long',
                  year: 'numeric',
                  month: 'long',
                  day: 'numeric'
                })} at {selectedSlot}
              </p>
            </div>

            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
                <div>
                  <label htmlFor="clientName" className="block text-sm font-medium text-neutral-700 mb-2">
                    Full Name *
                  </label>
                  <input
                    type="text"
                    id="clientName"
                    name="clientName"
                    value={formData.clientName}
                    onChange={handleInputChange}
                    required
                    className="input-field"
                    placeholder="Your full name"
                  />
                </div>
                <div>
                  <label htmlFor="clientEmail" className="block text-sm font-medium text-neutral-700 mb-2">
                    Email *
                  </label>
                  <input
                    type="email"
                    id="clientEmail"
                    name="clientEmail"
                    value={formData.clientEmail}
                    onChange={handleInputChange}
                    required
                    className="input-field"
                    placeholder="<EMAIL>"
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
                <div>
                  <label htmlFor="clientPhone" className="block text-sm font-medium text-neutral-700 mb-2">
                    Phone Number *
                  </label>
                  <input
                    type="tel"
                    id="clientPhone"
                    name="clientPhone"
                    value={formData.clientPhone}
                    onChange={handleInputChange}
                    required
                    className="input-field"
                    placeholder="(*************"
                  />
                </div>
                <div>
                  <label htmlFor="sessionType" className="block text-sm font-medium text-neutral-700 mb-2">
                    Session Type
                  </label>
                  <select
                    id="sessionType"
                    name="sessionType"
                    value={formData.sessionType}
                    onChange={handleInputChange}
                    className="input-field"
                  >
                    {sessionTypes.map((type) => (
                      <option key={type.value} value={type.value}>
                        {type.label}
                      </option>
                    ))}
                  </select>
                </div>
              </div>

              <div>
                <label htmlFor="location" className="block text-sm font-medium text-neutral-700 mb-2">
                  Preferred Location
                </label>
                <select
                  id="location"
                  name="location"
                  value={formData.location}
                  onChange={handleInputChange}
                  className="input-field"
                >
                  {locations.map((location) => (
                    <option key={location.value} value={location.value}>
                      {location.label}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label htmlFor="notes" className="block text-sm font-medium text-neutral-700 mb-2">
                  Special Requests or Notes
                </label>
                <textarea
                  id="notes"
                  name="notes"
                  value={formData.notes}
                  onChange={handleInputChange}
                  rows={4}
                  className="input-field resize-none"
                  placeholder="Any special requests, themes, or important details..."
                />
              </div>

              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <button
                  type="button"
                  onClick={() => setStep(2)}
                  className="btn-secondary px-8 py-3"
                >
                  Back to Time Selection
                </button>
                <button
                  type="submit"
                  disabled={isSubmitting}
                  className="btn-primary px-8 py-3 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isSubmitting ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                      Submitting...
                    </>
                  ) : (
                    'Confirm Booking'
                  )}
                </button>
              </div>
            </form>
          </div>
        )}

        {step === 4 && bookingConfirmed && (
          <div className="text-center">
            <div className="bg-green-50 border border-green-200 rounded-lg p-8 max-w-2xl mx-auto">
              <div className="text-green-600 mb-6">
                <Check className="h-16 w-16 mx-auto" />
              </div>
              <h2 className="text-2xl font-serif font-semibold text-green-900 mb-4">
                Booking Request Submitted!
              </h2>
              <p className="text-green-700 mb-6">
                Thank you for your booking request. I'll review the details and confirm your appointment within 24 hours.
              </p>
              <div className="bg-white rounded-lg p-4 mb-6 text-left">
                <h3 className="font-medium text-neutral-900 mb-2">Booking Summary</h3>
                <div className="space-y-1 text-sm text-neutral-600">
                  <p><strong>Date:</strong> {selectedDate?.toLocaleDateString()}</p>
                  <p><strong>Time:</strong> {selectedSlot}</p>
                  <p><strong>Session:</strong> {sessionTypes.find(t => t.value === formData.sessionType)?.label}</p>
                  <p><strong>Location:</strong> {locations.find(l => l.value === formData.location)?.label}</p>
                </div>
              </div>
              <button
                onClick={resetBooking}
                className="btn-primary"
              >
                Book Another Session
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default BookingPage;
