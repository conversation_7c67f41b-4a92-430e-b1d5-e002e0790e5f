import { useState, useEffect } from 'react';
import { Routes, Route, Link, useLocation, useNavigate } from 'react-router-dom';
import { useApp } from '../../context/AppContext';
import { 
  LayoutDashboard, 
  Camera, 
  Calendar, 
  Mail, 
  Users, 
  Settings,
  LogOut,
  Menu,
  X
} from 'lucide-react';
import axios from 'axios';

const AdminDashboard = () => {
  const { state, dispatch, actionTypes } = useApp();
  const location = useLocation();
  const navigate = useNavigate();
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [dashboardStats, setDashboardStats] = useState(null);
  const [loading, setLoading] = useState(true);

  const navigation = [
    { name: 'Dashboard', href: '/admin', icon: LayoutDashboard },
    { name: 'Photos', href: '/admin/photos', icon: Camera },
    { name: 'Bookings', href: '/admin/bookings', icon: Calendar },
    { name: 'Contacts', href: '/admin/contacts', icon: Mail },
    { name: 'Proofing Galleries', href: '/admin/proofing', icon: Users },
    { name: 'Settings', href: '/admin/settings', icon: Settings },
  ];

  useEffect(() => {
    fetchDashboardStats();
  }, []);

  const fetchDashboardStats = async () => {
    try {
      const response = await axios.get('/admin/dashboard');
      setDashboardStats(response.data);
    } catch (error) {
      console.error('Error fetching dashboard stats:', error);
      dispatch({
        type: actionTypes.SET_ERROR,
        payload: 'Failed to load dashboard statistics'
      });
    } finally {
      setLoading(false);
    }
  };

  const handleLogout = async () => {
    try {
      const refreshToken = localStorage.getItem('refreshToken');
      await axios.post('/admin/logout', { refreshToken });
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      dispatch({ type: actionTypes.LOGOUT });
      navigate('/admin/login');
    }
  };

  const isActive = (path) => {
    if (path === '/admin' && location.pathname === '/admin') return true;
    if (path !== '/admin' && location.pathname.startsWith(path)) return true;
    return false;
  };

  const DashboardHome = () => (
    <div>
      <div className="mb-8">
        <h1 className="text-2xl font-serif font-bold text-neutral-900">Dashboard</h1>
        <p className="text-neutral-600">Welcome back, {state.admin?.name}</p>
      </div>

      {loading ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {[...Array(4)].map((_, i) => (
            <div key={i} className="bg-white rounded-lg shadow p-6 animate-pulse">
              <div className="h-4 bg-neutral-200 rounded w-3/4 mb-2"></div>
              <div className="h-8 bg-neutral-200 rounded w-1/2"></div>
            </div>
          ))}
        </div>
      ) : dashboardStats ? (
        <>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <Camera className="h-8 w-8 text-primary-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-neutral-600">Total Photos</p>
                  <p className="text-2xl font-semibold text-neutral-900">{dashboardStats.stats.photos}</p>
                </div>
              </div>
            </div>
            
            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <Calendar className="h-8 w-8 text-blue-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-neutral-600">Total Bookings</p>
                  <p className="text-2xl font-semibold text-neutral-900">{dashboardStats.stats.bookings}</p>
                </div>
              </div>
            </div>
            
            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <Mail className="h-8 w-8 text-green-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-neutral-600">Unread Messages</p>
                  <p className="text-2xl font-semibold text-neutral-900">{dashboardStats.stats.unreadContacts}</p>
                </div>
              </div>
            </div>
            
            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <Users className="h-8 w-8 text-purple-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-neutral-600">Active Galleries</p>
                  <p className="text-2xl font-semibold text-neutral-900">{dashboardStats.stats.activeProofingGalleries}</p>
                </div>
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Recent Bookings */}
            <div className="bg-white rounded-lg shadow">
              <div className="p-6 border-b border-neutral-200">
                <h3 className="text-lg font-medium text-neutral-900">Recent Bookings</h3>
              </div>
              <div className="p-6">
                {dashboardStats.recentActivity.bookings.length > 0 ? (
                  <div className="space-y-4">
                    {dashboardStats.recentActivity.bookings.map((booking) => (
                      <div key={booking._id} className="flex items-center justify-between">
                        <div>
                          <p className="font-medium text-neutral-900">{booking.clientName}</p>
                          <p className="text-sm text-neutral-600">
                            {new Date(booking.date).toLocaleDateString()} at {booking.timeSlot}
                          </p>
                        </div>
                        <span className={`px-2 py-1 text-xs rounded-full ${
                          booking.status === 'confirmed' ? 'bg-green-100 text-green-800' :
                          booking.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                          'bg-neutral-100 text-neutral-800'
                        }`}>
                          {booking.status}
                        </span>
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-neutral-600">No recent bookings</p>
                )}
              </div>
            </div>

            {/* Recent Contacts */}
            <div className="bg-white rounded-lg shadow">
              <div className="p-6 border-b border-neutral-200">
                <h3 className="text-lg font-medium text-neutral-900">Recent Messages</h3>
              </div>
              <div className="p-6">
                {dashboardStats.recentActivity.contacts.length > 0 ? (
                  <div className="space-y-4">
                    {dashboardStats.recentActivity.contacts.map((contact) => (
                      <div key={contact._id} className="flex items-center justify-between">
                        <div>
                          <p className="font-medium text-neutral-900">{contact.name}</p>
                          <p className="text-sm text-neutral-600">{contact.email}</p>
                        </div>
                        <span className={`px-2 py-1 text-xs rounded-full ${
                          contact.status === 'new' ? 'bg-blue-100 text-blue-800' :
                          contact.status === 'read' ? 'bg-yellow-100 text-yellow-800' :
                          'bg-green-100 text-green-800'
                        }`}>
                          {contact.status}
                        </span>
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-neutral-600">No recent messages</p>
                )}
              </div>
            </div>
          </div>
        </>
      ) : (
        <div className="text-center py-12">
          <p className="text-neutral-600">Failed to load dashboard data</p>
        </div>
      )}
    </div>
  );

  return (
    <div className="min-h-screen bg-neutral-50">
      {/* Mobile sidebar backdrop */}
      {sidebarOpen && (
        <div 
          className="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden"
          onClick={() => setSidebarOpen(false)}
        />
      )}

      {/* Sidebar */}
      <div className={`fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg transform transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0 ${
        sidebarOpen ? 'translate-x-0' : '-translate-x-full'
      }`}>
        <div className="flex items-center justify-between h-16 px-6 border-b border-neutral-200">
          <h2 className="text-lg font-serif font-semibold text-neutral-900">Admin Panel</h2>
          <button
            onClick={() => setSidebarOpen(false)}
            className="lg:hidden p-1 rounded-md hover:bg-neutral-100"
          >
            <X className="h-5 w-5" />
          </button>
        </div>
        
        <nav className="mt-6 px-3">
          <div className="space-y-1">
            {navigation.map((item) => (
              <Link
                key={item.name}
                to={item.href}
                className={`flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors duration-200 ${
                  isActive(item.href)
                    ? 'bg-primary-100 text-primary-700'
                    : 'text-neutral-700 hover:bg-neutral-100'
                }`}
                onClick={() => setSidebarOpen(false)}
              >
                <item.icon className="mr-3 h-5 w-5" />
                {item.name}
              </Link>
            ))}
          </div>
        </nav>

        <div className="absolute bottom-0 left-0 right-0 p-3">
          <button
            onClick={handleLogout}
            className="flex items-center w-full px-3 py-2 text-sm font-medium text-neutral-700 rounded-md hover:bg-neutral-100 transition-colors duration-200"
          >
            <LogOut className="mr-3 h-5 w-5" />
            Sign Out
          </button>
        </div>
      </div>

      {/* Main content */}
      <div className="lg:pl-64">
        {/* Top bar */}
        <div className="bg-white shadow-sm border-b border-neutral-200">
          <div className="flex items-center justify-between h-16 px-6">
            <button
              onClick={() => setSidebarOpen(true)}
              className="lg:hidden p-1 rounded-md hover:bg-neutral-100"
            >
              <Menu className="h-6 w-6" />
            </button>
            
            <div className="flex items-center space-x-4">
              <span className="text-sm text-neutral-600">
                {state.admin?.email}
              </span>
            </div>
          </div>
        </div>

        {/* Page content */}
        <div className="p-6">
          <Routes>
            <Route path="/" element={<DashboardHome />} />
            <Route path="/photos" element={<div>Photos Management (Coming Soon)</div>} />
            <Route path="/bookings" element={<div>Bookings Management (Coming Soon)</div>} />
            <Route path="/contacts" element={<div>Contacts Management (Coming Soon)</div>} />
            <Route path="/proofing" element={<div>Proofing Galleries (Coming Soon)</div>} />
            <Route path="/settings" element={<div>Settings (Coming Soon)</div>} />
          </Routes>
        </div>
      </div>
    </div>
  );
};

export default AdminDashboard;
