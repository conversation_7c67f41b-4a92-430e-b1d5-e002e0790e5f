import { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { useApp } from '../context/AppContext';
import { ChevronLeft, ChevronRight, Instagram, Facebook, Twitter } from 'lucide-react';
import axios from 'axios';

const HomePage = () => {
  const { state, dispatch, actionTypes } = useApp();
  const [highlightedPhotos, setHighlightedPhotos] = useState([]);
  const [currentSlide, setCurrentSlide] = useState(0);
  const [loading, setLoading] = useState(true);
  const [showCategories, setShowCategories] = useState(false);

  useEffect(() => {
    // Use slide images for main carousel
    const slideImages = [
      { url: '/templatesPhotos/slide14.jpg', title: 'Slide 1' },
      { url: '/templatesPhotos/slide18.jpg', title: 'Slide 2' },
      { url: '/templatesPhotos/slide20.jpg', title: 'Slide 3' }
    ];

    setHighlightedPhotos(slideImages);
    setLoading(false);
  }, []);

  // Navigation functions for the slider
  const nextSlide = () => {
    setCurrentSlide((prev) => (prev + 1) % Math.max(highlightedPhotos.length, 1));
  };

  const prevSlide = () => {
    setCurrentSlide((prev) => (prev - 1 + Math.max(highlightedPhotos.length, 1)) % Math.max(highlightedPhotos.length, 1));
  };

  // Auto-advance slides
  useEffect(() => {
    if (highlightedPhotos.length > 1 && !showCategories) {
      const timer = setInterval(nextSlide, 5000);
      return () => clearInterval(timer);
    }
  }, [highlightedPhotos.length, showCategories]);

  // Category data
  const categories = [
    {
      name: 'Portraits',
      image: '/templatesPhotos/1_1.png',
      description: 'Professional portrait photography'
    },
    {
      name: 'Lifestyle',
      image: '/templatesPhotos/1_2.png',
      description: 'Natural lifestyle moments'
    },
    {
      name: 'Studio',
      image: '/templatesPhotos/1_3.png',
      description: 'Controlled studio sessions'
    },
    {
      name: 'Fashion',
      image: '/templatesPhotos/slide14.jpg',
      description: 'Fashion and editorial photography'
    }
  ];

  if (loading) {
    return (
      <div className="min-h-screen bg-white flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-white relative">
      {/* Header */}
      <header className="absolute top-0 left-0 right-0 z-50 px-8 py-6">
        <div className="flex justify-between items-center">
          {/* Logo */}
          <div className="text-xl font-normal tracking-wider text-black">
            NOVO
          </div>

          {/* Navigation */}
          <nav className="flex space-x-8 text-xs tracking-widest text-black uppercase">
            <Link to="/" className="hover:opacity-70 transition-opacity">HOME</Link>
            <Link to="/gallery" className="hover:opacity-70 transition-opacity">GALLERY</Link>
            <Link to="/contact" className="hover:opacity-70 transition-opacity">CONTACTS</Link>
            <Link to="/booking" className="hover:opacity-70 transition-opacity">BOOKING</Link>
          </nav>
        </div>
      </header>

      {/* Left Sidebar - Social Media */}
      <div className="absolute left-6 top-1/2 transform -translate-y-1/2 z-40">
        <div className="flex flex-col items-center space-y-12">
          <div className="text-black text-xs tracking-widest transform -rotate-90 origin-center whitespace-nowrap mb-8">
            FOLLOW
          </div>
          <div className="flex flex-col space-y-6">
            <a href="#" className="text-black hover:opacity-70 transition-opacity">
              <Instagram size={18} />
            </a>
            <a href="#" className="text-black hover:opacity-70 transition-opacity">
              <Facebook size={18} />
            </a>
            <a href="#" className="text-black hover:opacity-70 transition-opacity">
              <Twitter size={18} />
            </a>
          </div>
        </div>
      </div>

      {/* Right Sidebar - Categories and About */}
      <div className="absolute right-6 top-1/2 transform -translate-y-1/2 z-40">
        <div className="text-right">
          <div className="text-black text-xs tracking-widest mb-8">
            CATEGORIES
          </div>
          <div className="flex flex-col space-y-4 text-black text-xs tracking-wider">
            <Link to="/gallery?category=portraits" className="hover:opacity-70 transition-opacity text-right">
              PORTRAITS
            </Link>
            <Link to="/gallery?category=lifestyle" className="hover:opacity-70 transition-opacity text-right">
              LIFESTYLE
            </Link>
            <Link to="/gallery?category=studio" className="hover:opacity-70 transition-opacity text-right">
              STUDIO
            </Link>
            <Link to="/gallery?category=fashion" className="hover:opacity-70 transition-opacity text-right">
              FASHION
            </Link>
          </div>

          <div className="text-black text-xs tracking-widest mt-16 mb-6">
            ABOUT
          </div>
          <div className="text-black text-xs max-w-xs text-right leading-relaxed">
            Professional photography capturing life's most precious moments with artistic vision and emotional depth.
          </div>
        </div>
      </div>

      {/* Main Content Area */}
      <main className="relative h-screen overflow-hidden bg-gray-100">
        {!showCategories ? (
          /* Single Hero Image */
          <div className="relative w-full h-full">
            {/* Background image */}
            <div
              className="absolute inset-0 bg-cover bg-center transition-all duration-1000 ease-in-out"
              style={{
                backgroundImage: `url(${highlightedPhotos[currentSlide]?.url || '/api/placeholder/1920/1080'})`,
              }}
            />

            {/* Content overlay - positioned to the left */}
            <div className="absolute left-0 top-0 bottom-0 flex items-center z-30 pl-24">
              <div className="text-left text-black max-w-lg">
                {/* Category label */}
                <div className="text-xs tracking-widest mb-6 opacity-70 uppercase">
                  Portraits
                </div>

                {/* Main heading */}
                <h1 className="text-5xl md:text-6xl font-light mb-8 leading-tight">
                  How to dissapear<br />
                  completely
                </h1>

                {/* Read more button */}
                <button className="text-xs tracking-widest border-b border-black pb-1 hover:opacity-70 transition-opacity uppercase">
                  READ MORE
                </button>
              </div>
            </div>

            {/* Navigation arrows - positioned at bottom center */}
            {highlightedPhotos.length > 1 && (
              <>
                <button
                  onClick={prevSlide}
                  className="absolute left-1/2 bottom-8 transform -translate-x-12 text-black hover:opacity-70 transition-opacity z-30"
                  aria-label="Previous image"
                >
                  <ChevronLeft size={24} />
                </button>
                <button
                  onClick={nextSlide}
                  className="absolute left-1/2 bottom-8 transform translate-x-12 text-black hover:opacity-70 transition-opacity z-30"
                  aria-label="Next image"
                >
                  <ChevronRight size={24} />
                </button>
              </>
            )}

            {/* Slide numbers */}
            <div className="absolute bottom-8 right-8 z-40">
              <div className="flex space-x-3 text-black text-sm">
                <span>01</span>
                <span>02</span>
                <span>03</span>
              </div>
            </div>
          </div>
        ) : (
          /* Category Grid */
          <div className="grid grid-cols-2 h-full">
            {categories.map((category, index) => (
              <div key={category.name} className="relative group overflow-hidden cursor-pointer">
                <img
                  src={category.image}
                  alt={category.name}
                  className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
                />
                <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-300" />
                <div className="absolute bottom-8 left-8 text-white z-10 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                  <h3 className="text-2xl font-light tracking-wide">{category.name}</h3>
                </div>
              </div>
            ))}

            {/* Navigation arrows for category view */}
            <button
              onClick={() => setShowCategories(false)}
              className="absolute left-1/2 bottom-8 transform -translate-x-12 text-black hover:opacity-70 transition-opacity z-30"
              aria-label="Back to main view"
            >
              <ChevronLeft size={24} />
            </button>
            <button
              onClick={() => setShowCategories(false)}
              className="absolute left-1/2 bottom-8 transform translate-x-12 text-black hover:opacity-70 transition-opacity z-30"
              aria-label="Back to main view"
            >
              <ChevronRight size={24} />
            </button>

            {/* Category slide numbers */}
            <div className="absolute bottom-8 right-8 z-40">
              <div className="flex space-x-2 text-black text-sm">
                <span>01</span>
                <span className="opacity-50">/</span>
                <span className="opacity-50">03</span>
              </div>
            </div>
          </div>
        )}
      </main>

    </div>
  );
};

export default HomePage;
