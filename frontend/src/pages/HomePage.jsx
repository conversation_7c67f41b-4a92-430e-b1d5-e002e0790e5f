import { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { useApp } from '../context/AppContext';
import { Camera, Heart, Baby, Users, Star } from 'lucide-react';
import axios from 'axios';

const HomePage = () => {
  const { state, dispatch, actionTypes } = useApp();
  const [highlightedPhotos, setHighlightedPhotos] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchHighlightedPhotos = async () => {
      try {
        setLoading(true);
        const response = await axios.get('/gallery/highlighted?limit=6');
        setHighlightedPhotos(response.data.photos);
      } catch (error) {
        console.error('Error fetching highlighted photos:', error);
        dispatch({
          type: actionTypes.SET_ERROR,
          payload: 'Failed to load featured photos'
        });
      } finally {
        setLoading(false);
      }
    };

    fetchHighlightedPhotos();
  }, [dispatch, actionTypes]);

  const services = [
    {
      icon: Heart,
      title: 'Pregnancy',
      description: 'Capturing the beautiful journey of motherhood with elegant maternity portraits.',
      color: 'text-pink-600'
    },
    {
      icon: Baby,
      title: 'Newborns',
      description: 'Precious first moments with your little one in a safe and comfortable environment.',
      color: 'text-blue-600'
    },
    {
      icon: Users,
      title: 'Follow-ups',
      description: 'Documenting your family\'s growth with milestone and family portrait sessions.',
      color: 'text-green-600'
    },
    {
      icon: Star,
      title: 'Special Events',
      description: 'Baptisms, communions, and other meaningful celebrations in your family\'s life.',
      color: 'text-purple-600'
    }
  ];

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative bg-gradient-to-br from-primary-50 to-neutral-100 py-20 lg:py-32">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-4xl md:text-6xl font-serif font-bold text-neutral-900 mb-6">
              Capturing Life's
              <span className="text-primary-600 block">Precious Moments</span>
            </h1>
            <p className="text-xl text-neutral-600 mb-8 max-w-3xl mx-auto">
              Specializing in pregnancy, newborn, and family photography. 
              Creating timeless memories that you'll treasure forever.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                to="/gallery"
                className="btn-primary inline-flex items-center justify-center px-8 py-3 text-lg"
              >
                <Camera className="mr-2 h-5 w-5" />
                View Gallery
              </Link>
              <Link
                to="/booking"
                className="btn-secondary inline-flex items-center justify-center px-8 py-3 text-lg"
              >
                Book Session
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Featured Photos Section */}
      <section className="py-16 lg:py-24 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-serif font-bold text-neutral-900 mb-4">
              Featured Work
            </h2>
            <p className="text-lg text-neutral-600 max-w-2xl mx-auto">
              A glimpse into the beautiful moments I've had the privilege to capture
            </p>
          </div>

          {loading ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {[...Array(6)].map((_, index) => (
                <div key={index} className="aspect-square bg-neutral-200 rounded-lg animate-pulse" />
              ))}
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {highlightedPhotos.map((photo) => (
                <div
                  key={photo._id}
                  className="group relative aspect-square overflow-hidden rounded-lg shadow-md hover:shadow-xl transition-shadow duration-300"
                >
                  <img
                    src={photo.fullUrl || photo.url}
                    alt={photo.title}
                    className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                  <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-opacity duration-300 flex items-end">
                    <div className="p-4 text-white opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                      <h3 className="font-medium">{photo.title}</h3>
                      <p className="text-sm capitalize">{photo.category.replace('-', ' ')}</p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}

          <div className="text-center mt-12">
            <Link
              to="/gallery"
              className="btn-primary inline-flex items-center justify-center px-6 py-3"
            >
              View Full Gallery
            </Link>
          </div>
        </div>
      </section>

      {/* Services Section */}
      <section className="py-16 lg:py-24 bg-neutral-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-serif font-bold text-neutral-900 mb-4">
              Photography Services
            </h2>
            <p className="text-lg text-neutral-600 max-w-2xl mx-auto">
              Specialized sessions designed to capture your family's unique story
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {services.map((service) => (
              <div
                key={service.title}
                className="text-center p-6 bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow duration-300"
              >
                <service.icon className={`h-12 w-12 mx-auto mb-4 ${service.color}`} />
                <h3 className="text-xl font-serif font-semibold text-neutral-900 mb-3">
                  {service.title}
                </h3>
                <p className="text-neutral-600">
                  {service.description}
                </p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 lg:py-24 bg-primary-600">
        <div className="max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8">
          <h2 className="text-3xl md:text-4xl font-serif font-bold text-white mb-6">
            Ready to Create Beautiful Memories?
          </h2>
          <p className="text-xl text-primary-100 mb-8">
            Let's discuss your vision and create something magical together.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              to="/contact"
              className="bg-white text-primary-600 hover:bg-neutral-100 font-medium py-3 px-8 rounded-lg transition-colors duration-200"
            >
              Get in Touch
            </Link>
            <Link
              to="/booking"
              className="border-2 border-white text-white hover:bg-white hover:text-primary-600 font-medium py-3 px-8 rounded-lg transition-colors duration-200"
            >
              Book Now
            </Link>
          </div>
        </div>
      </section>
    </div>
  );
};

export default HomePage;
