import { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { useApp } from '../context/AppContext';
import { ChevronLeft, ChevronRight, Instagram, Facebook, Twitter } from 'lucide-react';
import axios from 'axios';

const HomePage = () => {
  const { state, dispatch, actionTypes } = useApp();
  const [highlightedPhotos, setHighlightedPhotos] = useState([]);
  const [currentSlide, setCurrentSlide] = useState(0);
  const [loading, setLoading] = useState(true);
  const [showCategories, setShowCategories] = useState(false);

  useEffect(() => {
    // Use slide images for main carousel
    const slideImages = [
      { url: '/templatesPhotos/slide14.jpg', title: 'Slide 1' },
      { url: '/templatesPhotos/slide18.jpg', title: 'Slide 2' },
      { url: '/templatesPhotos/slide20.jpg', title: 'Slide 3' }
    ];

    setHighlightedPhotos(slideImages);
    setLoading(false);
  }, []);

  // Navigation functions for the slider
  const nextSlide = () => {
    setCurrentSlide((prev) => (prev + 1) % Math.max(highlightedPhotos.length, 1));
  };

  const prevSlide = () => {
    setCurrentSlide((prev) => (prev - 1 + Math.max(highlightedPhotos.length, 1)) % Math.max(highlightedPhotos.length, 1));
  };

  // Auto-advance slides
  useEffect(() => {
    if (highlightedPhotos.length > 1 && !showCategories) {
      const timer = setInterval(nextSlide, 5000);
      return () => clearInterval(timer);
    }
  }, [highlightedPhotos.length, showCategories]);

  // Category data
  const categories = [
    {
      name: 'Portraits',
      image: '/templatesPhotos/1_1.png',
      description: 'Professional portrait photography'
    },
    {
      name: 'Lifestyle',
      image: '/templatesPhotos/1_2.png',
      description: 'Natural lifestyle moments'
    },
    {
      name: 'Studio',
      image: '/templatesPhotos/1_3.png',
      description: 'Controlled studio sessions'
    },
    {
      name: 'Fashion',
      image: '/templatesPhotos/slide14.jpg',
      description: 'Fashion and editorial photography'
    }
  ];

  if (loading) {
    return (
      <div className="min-h-screen bg-white flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-white relative">
      {/* Header */}
      <header className="absolute top-0 left-0 right-0 z-50 px-8 py-6">
        <div className="flex justify-between items-center">
          {/* Logo */}
          <div className="text-xl font-normal tracking-wider text-black">
            NOVO
          </div>

          {/* Navigation */}
          <nav className="flex space-x-8 text-xs tracking-widest text-black uppercase">
            <Link to="/" className="hover:opacity-70 transition-opacity">HOME</Link>
            <Link to="/gallery" className="hover:opacity-70 transition-opacity">GALLERY</Link>
            <Link to="/contact" className="hover:opacity-70 transition-opacity">CONTACTS</Link>
            <Link to="/booking" className="hover:opacity-70 transition-opacity">BOOKING</Link>
          </nav>
        </div>
      </header>

      {/* Left Sidebar - Social Media */}
      <div className="absolute left-8 top-1/2 transform -translate-y-1/2 z-40">
        <div className="flex flex-col space-y-6">
          <a href="#" className="flex items-center space-x-3 text-black hover:opacity-70 transition-opacity group">
            <Facebook size={16} />
            <span className="text-xs tracking-wider opacity-0 group-hover:opacity-100 transition-opacity">Facebook</span>
          </a>
          <a href="#" className="flex items-center space-x-3 text-black hover:opacity-70 transition-opacity group">
            <Instagram size={16} />
            <span className="text-xs tracking-wider opacity-0 group-hover:opacity-100 transition-opacity">Instagram</span>
          </a>
          <a href="#" className="flex items-center space-x-3 text-black hover:opacity-70 transition-opacity group">
            <Twitter size={16} />
            <span className="text-xs tracking-wider opacity-0 group-hover:opacity-100 transition-opacity">Twitter</span>
          </a>
        </div>
      </div>

      {/* Right Sidebar - Categories and About Buttons */}
      <div className="absolute right-8 top-1/2 transform -translate-y-1/2 z-40">
        <div className="flex flex-col space-y-8">
          <button
            onClick={() => setShowCategories(!showCategories)}
            className="flex flex-col items-center space-y-2 text-black hover:opacity-70 transition-opacity group"
          >
            <span className="text-xs tracking-widest">Categories</span>
            <span className="text-xs opacity-0 group-hover:opacity-100 transition-opacity">
              {showCategories ? 'Close' : ''}
            </span>
          </button>

          <button className="flex flex-col items-center space-y-2 text-black hover:opacity-70 transition-opacity group">
            <span className="text-xs tracking-widest">About</span>
            <span className="text-xs opacity-0 group-hover:opacity-100 transition-opacity">Close</span>
          </button>
        </div>
      </div>

      {/* Main Content Area */}
      <main className="relative h-screen overflow-hidden bg-gray-100">
        {!showCategories ? (
          /* Single Hero Image */
          <div className="relative w-full h-full">
            {/* Background image */}
            <div
              className="absolute inset-0 bg-cover bg-center transition-all duration-1000 ease-in-out"
              style={{
                backgroundImage: `url(${highlightedPhotos[currentSlide]?.url || '/api/placeholder/1920/1080'})`,
              }}
            />

            {/* Content overlay - positioned at bottom left like template */}
            <div className="absolute left-0 bottom-0 z-30 p-16">
              <div className="text-left text-black max-w-lg">
                {/* Heading with number and category */}
                <div className="mb-6">
                  <div className="flex items-start space-x-4 mb-4">
                    <div className="text-xs tracking-widest opacity-70"></div>
                    <div className="text-xs tracking-widest opacity-70 uppercase">
                      Portraits
                    </div>
                  </div>

                  {/* Main heading */}
                  <h2 className="text-5xl md:text-6xl font-light leading-tight">
                    How to dissapear<br />
                    completely
                  </h2>
                </div>

                {/* Read more button */}
                <button className="text-xs tracking-widest border border-black px-6 py-3 hover:bg-black hover:text-white transition-all uppercase">
                  <span>READ MORE</span>
                </button>
              </div>
            </div>

            {/* Navigation arrows - positioned at bottom right like template */}
            {highlightedPhotos.length > 1 && (
              <div className="absolute bottom-8 right-8 flex space-x-4 z-30">
                <button
                  onClick={prevSlide}
                  className="text-black hover:opacity-70 transition-opacity"
                  aria-label="Previous image"
                >
                  <ChevronLeft size={20} />
                </button>
                <button
                  onClick={nextSlide}
                  className="text-black hover:opacity-70 transition-opacity"
                  aria-label="Next image"
                >
                  <ChevronRight size={20} />
                </button>
              </div>
            )}

            {/* Slide dots - positioned at bottom right like template */}
            <div className="absolute bottom-8 right-24 z-40">
              <div className="flex space-x-2">
                {highlightedPhotos.map((_, index) => (
                  <button
                    key={index}
                    onClick={() => setCurrentSlide(index)}
                    className={`w-2 h-2 rounded-full transition-all ${index === currentSlide ? 'bg-black' : 'bg-black bg-opacity-30'
                      }`}
                    aria-label={`Go to slide ${index + 1}`}
                  />
                ))}
              </div>
            </div>
          </div>
        ) : (
          /* Category Grid - Vertical layout like template */
          <div className="flex flex-col h-full">
            {categories.map((category, index) => (
              <div key={category.name} className="relative flex-1 group overflow-hidden cursor-pointer">
                <div
                  className="w-full h-full bg-cover bg-center transition-transform duration-300 group-hover:scale-105"
                  style={{
                    backgroundImage: `url(${category.image})`,
                  }}
                />
                <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-300" />
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="text-center text-white">
                    <div className="text-xs tracking-widest mb-2 opacity-70"></div>
                    <h3 className="text-2xl font-light tracking-wide">{category.name}</h3>
                  </div>
                </div>
              </div>
            ))}

            {/* Close button for category view */}
            <button
              onClick={() => setShowCategories(false)}
              className="absolute top-8 right-8 text-white hover:opacity-70 transition-opacity z-30"
              aria-label="Close categories"
            >
              <span className="text-xs tracking-widest">Close</span>
            </button>
          </div>
        )}
      </main>

    </div>
  );
};

export default HomePage;
