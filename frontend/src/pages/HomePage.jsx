import { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { useApp } from '../context/AppContext';
import { ChevronLeft, ChevronRight, Instagram, Facebook, Twitter } from 'lucide-react';
import axios from 'axios';

const HomePage = () => {
  const { state, dispatch, actionTypes } = useApp();
  const [highlightedPhotos, setHighlightedPhotos] = useState([]);
  const [currentSlide, setCurrentSlide] = useState(0);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchHighlightedPhotos = async () => {
      try {
        setLoading(true);
        const response = await axios.get('/gallery/highlighted?limit=6');
        setHighlightedPhotos(response.data.photos);
      } catch (error) {
        console.error('Error fetching highlighted photos:', error);
        dispatch({
          type: actionTypes.SET_ERROR,
          payload: 'Failed to load featured photos'
        });
      } finally {
        setLoading(false);
      }
    };

    fetchHighlightedPhotos();
  }, [dispatch, actionTypes]);

  // Navigation functions for the slider
  const nextSlide = () => {
    setCurrentSlide((prev) => (prev + 1) % Math.max(highlightedPhotos.length, 1));
  };

  const prevSlide = () => {
    setCurrentSlide((prev) => (prev - 1 + Math.max(highlightedPhotos.length, 1)) % Math.max(highlightedPhotos.length, 1));
  };

  // Auto-advance slides
  useEffect(() => {
    if (highlightedPhotos.length > 1) {
      const timer = setInterval(nextSlide, 5000);
      return () => clearInterval(timer);
    }
  }, [highlightedPhotos.length]);

  if (loading) {
    return (
      <div className="min-h-screen bg-white flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-white relative">
      {/* Header */}
      <header className="absolute top-0 left-0 right-0 z-50 p-8">
        <div className="flex justify-between items-center">
          {/* Logo */}
          <div className="text-2xl font-light tracking-wider text-white">
            LiMarty
          </div>

          {/* Navigation */}
          <nav className="flex space-x-8 text-sm tracking-wider text-white">
            <Link to="/" className="hover:opacity-70 transition-opacity">HOME</Link>
            <Link to="/gallery" className="hover:opacity-70 transition-opacity">GALLERY</Link>
            <Link to="/contact" className="hover:opacity-70 transition-opacity">CONTACTS</Link>
            <Link to="/booking" className="hover:opacity-70 transition-opacity">BOOKING</Link>
          </nav>
        </div>
      </header>

      {/* Left Sidebar - Social Media */}
      <div className="absolute left-8 top-1/2 transform -translate-y-1/2 z-40">
        <div className="flex flex-col space-y-6">
          <div className="text-white text-xs tracking-widest transform -rotate-90 origin-center mb-8">
            FOLLOW
          </div>
          <a href="#" className="text-white hover:opacity-70 transition-opacity">
            <Instagram size={20} />
          </a>
          <a href="#" className="text-white hover:opacity-70 transition-opacity">
            <Facebook size={20} />
          </a>
          <a href="#" className="text-white hover:opacity-70 transition-opacity">
            <Twitter size={20} />
          </a>
        </div>
      </div>

      {/* Right Sidebar - Categories and About */}
      <div className="absolute right-8 top-1/2 transform -translate-y-1/2 z-40">
        <div className="text-right">
          <div className="text-white text-xs tracking-widest mb-8">
            CATEGORIES
          </div>
          <div className="flex flex-col space-y-4 text-white text-sm">
            <Link to="/gallery?category=portraits" className="hover:opacity-70 transition-opacity">
              Portraits
            </Link>
            <Link to="/gallery?category=pregnancy" className="hover:opacity-70 transition-opacity">
              Pregnancy
            </Link>
            <Link to="/gallery?category=newborn" className="hover:opacity-70 transition-opacity">
              Newborn
            </Link>
            <Link to="/gallery?category=family" className="hover:opacity-70 transition-opacity">
              Family
            </Link>
          </div>

          <div className="text-white text-xs tracking-widest mt-12 mb-4">
            ABOUT
          </div>
          <div className="text-white text-sm max-w-xs">
            Professional photography capturing life's most precious moments with artistic vision and emotional depth.
          </div>
        </div>
      </div>

      {/* Full-screen hero slider */}
      <section className="relative h-screen overflow-hidden">
        {highlightedPhotos.length > 0 ? (
          <div className="relative w-full h-full">
            {/* Background image */}
            <div
              className="absolute inset-0 bg-cover bg-center transition-all duration-1000 ease-in-out"
              style={{
                backgroundImage: `url(${highlightedPhotos[currentSlide]?.url || '/api/placeholder/1920/1080'})`,
              }}
            >
              <div className="absolute inset-0 bg-black/20"></div>
            </div>

            {/* Navigation arrows - positioned at bottom center */}
            {highlightedPhotos.length > 1 && (
              <>
                <button
                  onClick={prevSlide}
                  className="absolute left-1/2 bottom-8 transform -translate-x-12 text-white hover:opacity-70 transition-opacity z-30"
                  aria-label="Previous image"
                >
                  <ChevronLeft size={24} />
                </button>
                <button
                  onClick={nextSlide}
                  className="absolute left-1/2 bottom-8 transform translate-x-12 text-white hover:opacity-70 transition-opacity z-30"
                  aria-label="Next image"
                >
                  <ChevronRight size={24} />
                </button>
              </>
            )}
          </div>
        ) : (
          // Fallback when no photos are available
          <div
            className="absolute inset-0 bg-cover bg-center"
            style={{
              backgroundImage: `url('/api/placeholder/1920/1080')`,
            }}
          >
            <div className="absolute inset-0 bg-black/20"></div>
          </div>
        )}

        {/* Content overlay */}
        <div className="absolute inset-0 flex items-center justify-center z-30">
          <div className="text-center text-white max-w-2xl px-8">
            {/* Category label */}
            <div className="text-sm tracking-widest mb-4 opacity-80">
              Portraits
            </div>

            {/* Main heading */}
            <h1 className="text-5xl md:text-6xl font-light mb-6 leading-tight">
              How to dissapear<br />
              completely
            </h1>

            {/* Read more button */}
            <button className="text-sm tracking-widest border-b border-white pb-1 hover:opacity-70 transition-opacity">
              READ MORE
            </button>
          </div>
        </div>

        {/* Slide numbers */}
        {highlightedPhotos.length > 1 && (
          <div className="absolute bottom-8 right-8 z-40">
            <div className="flex space-x-2 text-white text-sm">
              <span>{String(currentSlide + 1).padStart(2, '0')}</span>
              <span className="opacity-50">/</span>
              <span className="opacity-50">{String(highlightedPhotos.length).padStart(2, '0')}</span>
            </div>
          </div>
        )}
      </section>

    </div>
  );
};

export default HomePage;
