import { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { useApp } from '../context/AppContext';
import { ChevronLeft, ChevronRight, Camera, Heart, Baby, Users, Star } from 'lucide-react';
import axios from 'axios';

const HomePage = () => {
  const { state, dispatch, actionTypes } = useApp();
  const [highlightedPhotos, setHighlightedPhotos] = useState([]);
  const [currentSlide, setCurrentSlide] = useState(0);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchHighlightedPhotos = async () => {
      try {
        setLoading(true);
        const response = await axios.get('/gallery/highlighted?limit=6');
        setHighlightedPhotos(response.data.photos);
      } catch (error) {
        console.error('Error fetching highlighted photos:', error);
        dispatch({
          type: actionTypes.SET_ERROR,
          payload: 'Failed to load featured photos'
        });
      } finally {
        setLoading(false);
      }
    };

    fetchHighlightedPhotos();
  }, [dispatch, actionTypes]);

  // Navigation functions for the slider
  const nextSlide = () => {
    setCurrentSlide((prev) => (prev + 1) % Math.max(highlightedPhotos.length, 1));
  };

  const prevSlide = () => {
    setCurrentSlide((prev) => (prev - 1 + Math.max(highlightedPhotos.length, 1)) % Math.max(highlightedPhotos.length, 1));
  };

  // Auto-advance slides
  useEffect(() => {
    if (highlightedPhotos.length > 1) {
      const timer = setInterval(nextSlide, 5000);
      return () => clearInterval(timer);
    }
  }, [highlightedPhotos.length]);

  const services = [
    {
      icon: Heart,
      title: 'Pregnancy',
      description: 'Capturing the beautiful journey of motherhood with elegant maternity portraits.',
      color: 'text-pink-600'
    },
    {
      icon: Baby,
      title: 'Newborns',
      description: 'Precious first moments with your little one in a safe and comfortable environment.',
      color: 'text-blue-600'
    },
    {
      icon: Users,
      title: 'Follow-ups',
      description: 'Documenting your family\'s growth with milestone and family portrait sessions.',
      color: 'text-green-600'
    },
    {
      icon: Star,
      title: 'Special Events',
      description: 'Baptisms, communions, and other meaningful celebrations in your family\'s life.',
      color: 'text-purple-600'
    }
  ];

  if (loading) {
    return (
      <div className="min-h-screen bg-white flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-white relative">
      {/* Full-screen hero slider */}
      <section className="relative h-screen overflow-hidden">
        {highlightedPhotos.length > 0 ? (
          <div className="relative w-full h-full">
            {/* Background image */}
            <div
              className="absolute inset-0 bg-cover bg-center transition-all duration-1000 ease-in-out"
              style={{
                backgroundImage: `url(${highlightedPhotos[currentSlide]?.url || '/api/placeholder/1920/1080'})`,
              }}
            >
              <div className="absolute inset-0 bg-black/20"></div>
            </div>

            {/* Navigation arrows */}
            {highlightedPhotos.length > 1 && (
              <>
                <button
                  onClick={prevSlide}
                  className="absolute left-6 top-1/2 transform -translate-y-1/2 z-20 text-white hover:text-gray-300 transition-colors"
                  aria-label="Previous image"
                >
                  <ChevronLeft className="w-8 h-8" />
                </button>
                <button
                  onClick={nextSlide}
                  className="absolute right-6 top-1/2 transform -translate-y-1/2 z-20 text-white hover:text-gray-300 transition-colors"
                  aria-label="Next image"
                >
                  <ChevronRight className="w-8 h-8" />
                </button>
              </>
            )}
          </div>
        ) : (
          // Fallback when no photos are available
          <div
            className="absolute inset-0 bg-cover bg-center"
            style={{
              backgroundImage: `url('/api/placeholder/1920/1080')`,
            }}
          >
            <div className="absolute inset-0 bg-black/20"></div>
          </div>
        )}

        {/* Content overlay */}
        <div className="absolute inset-0 flex items-center justify-center z-10">
          <div className="text-center text-white px-4 max-w-4xl mx-auto">
            {/* Category label */}
            <div className="mb-6">
              <span className="text-sm uppercase tracking-wider text-gray-300">Portraits</span>
            </div>

            {/* Main heading */}
            <h1 className="text-4xl md:text-6xl lg:text-7xl font-light mb-8 leading-tight">
              How to dissapear
              <br />
              <span className="font-normal">completely</span>
            </h1>

            {/* Read more button */}
            <div className="mb-12">
              <button className="text-sm uppercase tracking-wider text-white hover:text-gray-300 transition-colors border-b border-white hover:border-gray-300 pb-1">
                Read More
              </button>
            </div>
          </div>
        </div>

        {/* Navigation dots */}
        {highlightedPhotos.length > 1 && (
          <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 z-20 flex space-x-2">
            {highlightedPhotos.map((_, index) => (
              <button
                key={index}
                onClick={() => setCurrentSlide(index)}
                className={`w-2 h-2 rounded-full transition-colors ${index === currentSlide ? 'bg-white' : 'bg-white/50'
                  }`}
                aria-label={`Go to slide ${index + 1}`}
              />
            ))}
          </div>
        )}

        {/* Slide numbers */}
        {highlightedPhotos.length > 1 && (
          <div className="absolute bottom-8 right-8 z-20 text-white text-sm">
            <span className="text-lg font-light">
              {String(currentSlide + 1).padStart(2, '0')}
            </span>
            <span className="mx-2 text-white/60">/</span>
            <span className="text-white/60">
              {String(highlightedPhotos.length).padStart(2, '0')}
            </span>
          </div>
        )}
      </section>

    </div>
  );
};

export default HomePage;
