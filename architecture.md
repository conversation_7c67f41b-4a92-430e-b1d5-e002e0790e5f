# 🏗️ Photographer <PERSON><PERSON><PERSON> App - Architecture

## 🖥️ Frontend

- **Framework:** React
- **Routing:** React Router DOM
- **State Management:** Context API (for user and gallery state)
- **Styling:** Tailwind CSS or Styled Components
- **API Calls:** Axios

### Components

1. **Navbar**
   - Links: Home, Gallery, Contacts, Booking
2. **HomePage**
   - Hero section with highlight images.
3. **GalleryPage**
   - Grid displaying categories and images.
   - Integrates **Instagram API** and database results.
4. **ProofingGalleryPage**
   - Password input form.
   - Displays images for the specific client (identified by **URL parameter**).
   - Allows **selecting** and **downloading** images.
5. **ContactPage**
   - Contact information with embedded Google Maps.
   - Contact form.
6. **BookingPage**
   - Calendar (e.g. react-calendar or FullCalendar).
   - Shows time slots after date selection.
   - Form to collect Name, Email, Phone.

---

## 🗄️ Backend

- **Framework:** Node.js with Express
- **Database:** MongoDB (with Mongoose)
- **Authentication:** JWT for admin routes

### API Endpoints

| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | /api/gallery | Get images for gallery |
| POST | /api/proofing/login | Validate proofing gallery password |
| GET | /api/proofing/:clientId | Get images for specific client gallery |
| POST | /api/booking | Create new booking |
| GET | /api/booking | List all bookings |
| POST | /api/contact | Submit contact form |
| POST | /api/admin/login | Admin login |
| CRUD | /api/admin/photos | Manage gallery photos |
| CRUD | /api/admin/proofing | Manage proofing galleries |
| CRUD | /api/admin/bookings | Manage bookings |

---

## 🔧 Backoffice

- **Stack:** React with protected routes or Next.js Admin Panel
- **Features:**
  - Upload/manage gallery photos.
  - Create/edit proofing galleries:
    - Upload/select images for client.
    - Define password.
    - Generate **unique client URL** for proofing gallery (e.g. `/proofing-gallery/:clientId`).
  - Manage bookings (dates, times, clients).

---

## ☁️ Integrations

- **Instagram API**
  - Fetch images via Graph API with OAuth.
- **Google Maps API**
  - Embed photographer’s location.
- **Email Service**
  - SMTP or SendGrid for contact forms and booking confirmations.

---

## 🔗 URL Generation for Proofing Galleries

1. **Backoffice Action:**
   - On creating a proofing gallery for a client:
     - Generates a **unique client ID**.
     - Creates URL: `https://yourdomain.com/proofing-gallery/:clientId`
     - Saves password hashed in DB associated with this client ID.

2. **Frontend Flow:**
   - When client accesses the URL:
     - Loads images assigned to that `clientId`.
     - Requires password entry before displaying images.
     - Enables image selection and download after authentication.

---

## ⚡ Deployment

- **Frontend:** Vercel or Netlify
- **Backend + DB:** AWS EC2 + MongoDB Atlas or Railway for rapid deployment
- **Domains & SSL:** Cloudflare

---

## 🔍 Notes

- Protect environment variables for API keys and database credentials.
- Proofing galleries must enforce password security per client.
- Booking calendar must sync with DB availability in real-time.
- Implement minimal, elegant, photography-focused UI as per attached screenshots.

---
