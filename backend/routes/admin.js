import express from 'express';
import Admin from '../models/Admin.js';
import Photo from '../models/Photo.js';
import ProofingGallery from '../models/ProofingGallery.js';
import Booking from '../models/Booking.js';
import Contact from '../models/Contact.js';
import { authenticateToken, generateTokens, verifyRefreshToken } from '../middleware/auth.js';
import { uploadPhoto, uploadProofing, handleUploadError, getFileInfo, deleteFile } from '../middleware/upload.js';
import rateLimit from 'express-rate-limit';

const router = express.Router();

// Rate limiting for login attempts
const loginLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 5, // limit each IP to 5 login attempts per windowMs
  message: 'Too many login attempts, please try again later.',
  standardHeaders: true,
  legacyHeaders: false,
});

// Admin login
router.post('/login', loginLimiter, async (req, res) => {
  try {
    const { email, password } = req.body;

    if (!email || !password) {
      return res.status(400).json({ error: 'Email and password are required' });
    }

    const admin = await Admin.findOne({ email: email.toLowerCase() });

    if (!admin) {
      return res.status(401).json({ error: 'Invalid credentials' });
    }

    if (!admin.isActive) {
      return res.status(401).json({ error: 'Account is deactivated' });
    }

    if (admin.isLocked) {
      return res.status(423).json({ error: 'Account is temporarily locked due to too many failed attempts' });
    }

    const isValidPassword = await admin.comparePassword(password);

    if (!isValidPassword) {
      await admin.incLoginAttempts();
      return res.status(401).json({ error: 'Invalid credentials' });
    }

    // Reset login attempts on successful login
    if (admin.loginAttempts > 0) {
      await admin.resetLoginAttempts();
    }

    // Generate tokens
    const { accessToken, refreshToken } = generateTokens(admin._id);

    // Save refresh token
    await admin.addRefreshToken(refreshToken);

    // Update last login
    admin.lastLogin = new Date();
    await admin.save();

    res.json({
      success: true,
      message: 'Login successful',
      admin: {
        id: admin._id,
        email: admin.email,
        name: admin.name,
        role: admin.role
      },
      tokens: {
        accessToken,
        refreshToken
      }
    });
  } catch (error) {
    console.error('Admin login error:', error);
    res.status(500).json({ error: 'Login failed' });
  }
});

// Refresh access token
router.post('/refresh', async (req, res) => {
  try {
    const { refreshToken } = req.body;

    if (!refreshToken) {
      return res.status(400).json({ error: 'Refresh token is required' });
    }

    const admin = await verifyRefreshToken(refreshToken);

    // Generate new access token
    const { accessToken } = generateTokens(admin._id);

    res.json({
      success: true,
      accessToken
    });
  } catch (error) {
    console.error('Token refresh error:', error);
    res.status(401).json({ error: 'Invalid refresh token' });
  }
});

// Admin logout
router.post('/logout', authenticateToken, async (req, res) => {
  try {
    const { refreshToken } = req.body;

    if (refreshToken) {
      await req.admin.removeRefreshToken(refreshToken);
    }

    res.json({
      success: true,
      message: 'Logged out successfully'
    });
  } catch (error) {
    console.error('Admin logout error:', error);
    res.status(500).json({ error: 'Logout failed' });
  }
});

// Get admin profile
router.get('/profile', authenticateToken, async (req, res) => {
  try {
    res.json({
      admin: {
        id: req.admin._id,
        email: req.admin.email,
        name: req.admin.name,
        role: req.admin.role,
        lastLogin: req.admin.lastLogin,
        createdAt: req.admin.createdAt
      }
    });
  } catch (error) {
    console.error('Profile fetch error:', error);
    res.status(500).json({ error: 'Failed to fetch profile' });
  }
});

// Dashboard statistics
router.get('/dashboard', authenticateToken, async (req, res) => {
  try {
    const now = new Date();
    const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);

    const [
      totalPhotos,
      totalBookings,
      totalContacts,
      totalProofingGalleries,
      thisMonthBookings,
      pendingBookings,
      unreadContacts,
      activeProofingGalleries
    ] = await Promise.all([
      Photo.countDocuments(),
      Booking.countDocuments(),
      Contact.countDocuments({ isSpam: false }),
      ProofingGallery.countDocuments(),
      Booking.countDocuments({ createdAt: { $gte: startOfMonth } }),
      Booking.countDocuments({ status: 'pending' }),
      Contact.getUnreadCount(),
      ProofingGallery.countDocuments({ isActive: true, expiresAt: { $gt: now } })
    ]);

    // Recent activity
    const [recentBookings, recentContacts] = await Promise.all([
      Booking.find().sort({ createdAt: -1 }).limit(5).select('clientName date timeSlot status'),
      Contact.find({ isSpam: false }).sort({ createdAt: -1 }).limit(5).select('name email status createdAt')
    ]);

    res.json({
      stats: {
        photos: totalPhotos,
        bookings: totalBookings,
        contacts: totalContacts,
        proofingGalleries: totalProofingGalleries,
        thisMonthBookings,
        pendingBookings,
        unreadContacts,
        activeProofingGalleries
      },
      recentActivity: {
        bookings: recentBookings,
        contacts: recentContacts
      }
    });
  } catch (error) {
    console.error('Dashboard stats error:', error);
    res.status(500).json({ error: 'Failed to fetch dashboard statistics' });
  }
});

// PHOTO MANAGEMENT ROUTES

// Get all photos (admin view)
router.get('/photos', authenticateToken, async (req, res) => {
  try {
    const {
      category,
      isPublic,
      isHighlighted,
      limit = 50,
      page = 1,
      sortBy = 'createdAt',
      sortOrder = 'desc'
    } = req.query;

    const query = {};

    if (category && category !== 'all') {
      query.category = category.toLowerCase();
    }

    if (isPublic !== undefined) {
      query.isPublic = isPublic === 'true';
    }

    if (isHighlighted !== undefined) {
      query.isHighlighted = isHighlighted === 'true';
    }

    const skip = (parseInt(page) - 1) * parseInt(limit);

    const sort = {};
    sort[sortBy] = sortOrder === 'desc' ? -1 : 1;

    const photos = await Photo.find(query)
      .sort(sort)
      .limit(parseInt(limit))
      .skip(skip)
      .select('-__v');

    const total = await Photo.countDocuments(query);

    res.json({
      photos,
      pagination: {
        current: parseInt(page),
        total: Math.ceil(total / parseInt(limit)),
        count: photos.length,
        totalPhotos: total
      }
    });
  } catch (error) {
    console.error('Admin photos fetch error:', error);
    res.status(500).json({ error: 'Failed to fetch photos' });
  }
});

// Upload new photos
router.post('/photos', authenticateToken, uploadPhoto.array('photos', 10), handleUploadError, async (req, res) => {
  try {
    if (!req.files || req.files.length === 0) {
      return res.status(400).json({ error: 'No photos uploaded' });
    }

    const { category, isPublic = 'true', isHighlighted = 'false', tags } = req.body;

    if (!category) {
      return res.status(400).json({ error: 'Category is required' });
    }

    const validCategories = ['pregnancy', 'newborns', 'follow-ups', 'baptisms', 'communions'];
    if (!validCategories.includes(category.toLowerCase())) {
      return res.status(400).json({ error: 'Invalid category' });
    }

    const uploadedPhotos = [];

    for (const file of req.files) {
      const fileInfo = getFileInfo(file);

      const photo = new Photo({
        title: file.originalname.replace(/\.[^/.]+$/, ""), // Remove extension
        filename: fileInfo.filename,
        originalName: fileInfo.originalName,
        path: fileInfo.path,
        url: fileInfo.url,
        category: category.toLowerCase(),
        isPublic: isPublic === 'true',
        isHighlighted: isHighlighted === 'true',
        tags: tags ? tags.split(',').map(tag => tag.trim().toLowerCase()) : [],
        metadata: {
          size: fileInfo.size,
          mimetype: fileInfo.mimetype
        }
      });

      await photo.save();
      uploadedPhotos.push(photo);
    }

    res.status(201).json({
      success: true,
      message: `${uploadedPhotos.length} photo(s) uploaded successfully`,
      photos: uploadedPhotos
    });
  } catch (error) {
    console.error('Photo upload error:', error);
    res.status(500).json({ error: 'Failed to upload photos' });
  }
});

// Update photo
router.put('/photos/:id', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;
    const { title, description, category, isPublic, isHighlighted, tags, sortOrder } = req.body;

    const photo = await Photo.findById(id);

    if (!photo) {
      return res.status(404).json({ error: 'Photo not found' });
    }

    // Update fields if provided
    if (title !== undefined) photo.title = title.trim();
    if (description !== undefined) photo.description = description.trim();
    if (category !== undefined) {
      const validCategories = ['pregnancy', 'newborns', 'follow-ups', 'baptisms', 'communions'];
      if (!validCategories.includes(category.toLowerCase())) {
        return res.status(400).json({ error: 'Invalid category' });
      }
      photo.category = category.toLowerCase();
    }
    if (isPublic !== undefined) photo.isPublic = Boolean(isPublic);
    if (isHighlighted !== undefined) photo.isHighlighted = Boolean(isHighlighted);
    if (tags !== undefined) {
      photo.tags = Array.isArray(tags) ? tags.map(tag => tag.trim().toLowerCase()) : [];
    }
    if (sortOrder !== undefined) photo.sortOrder = parseInt(sortOrder) || 0;

    await photo.save();

    res.json({
      success: true,
      message: 'Photo updated successfully',
      photo
    });
  } catch (error) {
    console.error('Photo update error:', error);
    res.status(500).json({ error: 'Failed to update photo' });
  }
});

// Delete photo
router.delete('/photos/:id', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;

    const photo = await Photo.findById(id);

    if (!photo) {
      return res.status(404).json({ error: 'Photo not found' });
    }

    // Delete file from filesystem if it's not an Instagram photo
    if (!photo.instagramId && photo.path) {
      try {
        await deleteFile(photo.path);
      } catch (fileError) {
        console.error('File deletion error:', fileError);
        // Continue with database deletion even if file deletion fails
      }
    }

    await Photo.findByIdAndDelete(id);

    res.json({
      success: true,
      message: 'Photo deleted successfully'
    });
  } catch (error) {
    console.error('Photo deletion error:', error);
    res.status(500).json({ error: 'Failed to delete photo' });
  }
});

export default router;
