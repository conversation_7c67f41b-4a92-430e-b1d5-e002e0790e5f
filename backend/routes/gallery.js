import express from 'express';
import Photo from '../models/Photo.js';
import { optionalAuth } from '../middleware/auth.js';

const router = express.Router();

// Get all public photos for gallery
router.get('/', optionalAuth, async (req, res) => {
  try {
    const { category, limit = 20, page = 1, highlighted } = req.query;
    
    const query = { isPublic: true };
    
    // Filter by category if specified
    if (category && category !== 'all') {
      query.category = category.toLowerCase();
    }
    
    // Filter highlighted photos if specified
    if (highlighted === 'true') {
      query.isHighlighted = true;
    }
    
    const skip = (parseInt(page) - 1) * parseInt(limit);
    
    const photos = await Photo.find(query)
      .sort({ isHighlighted: -1, createdAt: -1, sortOrder: 1 })
      .limit(parseInt(limit))
      .skip(skip)
      .select('-__v');
    
    const total = await Photo.countDocuments(query);
    
    res.json({
      photos,
      pagination: {
        current: parseInt(page),
        total: Math.ceil(total / parseInt(limit)),
        count: photos.length,
        totalPhotos: total
      }
    });
  } catch (error) {
    console.error('Gallery fetch error:', error);
    res.status(500).json({ error: 'Failed to fetch gallery photos' });
  }
});

// Get photos by category
router.get('/category/:category', optionalAuth, async (req, res) => {
  try {
    const { category } = req.params;
    const { limit = 20, page = 1 } = req.query;
    
    const validCategories = ['pregnancy', 'newborns', 'follow-ups', 'baptisms', 'communions'];
    
    if (!validCategories.includes(category.toLowerCase())) {
      return res.status(400).json({ error: 'Invalid category' });
    }
    
    const query = { 
      isPublic: true, 
      category: category.toLowerCase() 
    };
    
    const skip = (parseInt(page) - 1) * parseInt(limit);
    
    const photos = await Photo.find(query)
      .sort({ createdAt: -1, sortOrder: 1 })
      .limit(parseInt(limit))
      .skip(skip)
      .select('-__v');
    
    const total = await Photo.countDocuments(query);
    
    res.json({
      category,
      photos,
      pagination: {
        current: parseInt(page),
        total: Math.ceil(total / parseInt(limit)),
        count: photos.length,
        totalPhotos: total
      }
    });
  } catch (error) {
    console.error('Category photos fetch error:', error);
    res.status(500).json({ error: 'Failed to fetch category photos' });
  }
});

// Get highlighted photos for homepage
router.get('/highlighted', async (req, res) => {
  try {
    const { limit = 6 } = req.query;
    
    const photos = await Photo.find({ 
      isPublic: true, 
      isHighlighted: true 
    })
      .sort({ sortOrder: 1, createdAt: -1 })
      .limit(parseInt(limit))
      .select('-__v');
    
    res.json({ photos });
  } catch (error) {
    console.error('Highlighted photos fetch error:', error);
    res.status(500).json({ error: 'Failed to fetch highlighted photos' });
  }
});

// Get categories with photo counts
router.get('/categories', async (req, res) => {
  try {
    const categories = await Photo.aggregate([
      { $match: { isPublic: true } },
      { 
        $group: { 
          _id: '$category', 
          count: { $sum: 1 },
          latestPhoto: { $first: '$$ROOT' }
        } 
      },
      { $sort: { _id: 1 } }
    ]);
    
    const formattedCategories = categories.map(cat => ({
      name: cat._id,
      displayName: cat._id.charAt(0).toUpperCase() + cat._id.slice(1).replace('-', ' '),
      count: cat.count,
      coverPhoto: cat.latestPhoto ? {
        url: cat.latestPhoto.url,
        title: cat.latestPhoto.title
      } : null
    }));
    
    res.json({ categories: formattedCategories });
  } catch (error) {
    console.error('Categories fetch error:', error);
    res.status(500).json({ error: 'Failed to fetch categories' });
  }
});

// Get single photo by ID
router.get('/:id', optionalAuth, async (req, res) => {
  try {
    const { id } = req.params;
    
    const photo = await Photo.findById(id).select('-__v');
    
    if (!photo) {
      return res.status(404).json({ error: 'Photo not found' });
    }
    
    // Check if photo is public or user is admin
    if (!photo.isPublic && !req.admin) {
      return res.status(403).json({ error: 'Photo not accessible' });
    }
    
    res.json({ photo });
  } catch (error) {
    console.error('Photo fetch error:', error);
    res.status(500).json({ error: 'Failed to fetch photo' });
  }
});

export default router;
