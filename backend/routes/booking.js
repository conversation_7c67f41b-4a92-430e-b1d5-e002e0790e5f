import express from 'express';
import Booking from '../models/Booking.js';
import { authenticateToken, optionalAuth } from '../middleware/auth.js';

const router = express.Router();

// Get available time slots for a specific date
router.get('/availability/:date', async (req, res) => {
  try {
    const { date } = req.params;
    
    // Validate date format
    const requestedDate = new Date(date);
    if (isNaN(requestedDate.getTime())) {
      return res.status(400).json({ error: 'Invalid date format' });
    }
    
    // Don't allow booking in the past
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    if (requestedDate < today) {
      return res.status(400).json({ error: 'Cannot book dates in the past' });
    }
    
    const availableSlots = await Booking.getAvailableTimeSlots(requestedDate);
    
    res.json({
      date: requestedDate.toISOString().split('T')[0],
      availableSlots,
      totalSlots: availableSlots.length
    });
  } catch (error) {
    console.error('Availability check error:', error);
    res.status(500).json({ error: 'Failed to check availability' });
  }
});

// Create a new booking
router.post('/', async (req, res) => {
  try {
    const {
      clientName,
      clientEmail,
      clientPhone,
      date,
      timeSlot,
      sessionType,
      location,
      notes
    } = req.body;
    
    // Validate required fields
    if (!clientName || !clientEmail || !clientPhone || !date || !timeSlot || !sessionType) {
      return res.status(400).json({ 
        error: 'Missing required fields',
        required: ['clientName', 'clientEmail', 'clientPhone', 'date', 'timeSlot', 'sessionType']
      });
    }
    
    // Validate date
    const bookingDate = new Date(date);
    if (isNaN(bookingDate.getTime())) {
      return res.status(400).json({ error: 'Invalid date format' });
    }
    
    // Don't allow booking in the past
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    if (bookingDate < today) {
      return res.status(400).json({ error: 'Cannot book dates in the past' });
    }
    
    // Check if time slot is available
    const availableSlots = await Booking.getAvailableTimeSlots(bookingDate);
    
    if (!availableSlots.includes(timeSlot)) {
      return res.status(409).json({ error: 'Time slot is not available' });
    }
    
    // Create booking
    const booking = new Booking({
      clientName: clientName.trim(),
      clientEmail: clientEmail.trim().toLowerCase(),
      clientPhone: clientPhone.trim(),
      date: bookingDate,
      timeSlot,
      sessionType: sessionType.toLowerCase(),
      location: location || 'studio',
      notes: notes?.trim()
    });
    
    await booking.save();
    
    res.status(201).json({
      success: true,
      message: 'Booking created successfully',
      booking: {
        id: booking._id,
        clientName: booking.clientName,
        date: booking.formattedDate,
        timeSlot: booking.timeSlot,
        sessionType: booking.sessionType,
        status: booking.status
      }
    });
  } catch (error) {
    if (error.code === 11000) {
      return res.status(409).json({ error: 'Time slot is already booked' });
    }
    
    console.error('Booking creation error:', error);
    res.status(500).json({ error: 'Failed to create booking' });
  }
});

// Get bookings (admin only)
router.get('/', authenticateToken, async (req, res) => {
  try {
    const { 
      status, 
      date, 
      sessionType, 
      limit = 50, 
      page = 1,
      sortBy = 'date',
      sortOrder = 'asc'
    } = req.query;
    
    const query = {};
    
    // Filter by status
    if (status) {
      query.status = status;
    }
    
    // Filter by date range
    if (date) {
      const filterDate = new Date(date);
      const startOfDay = new Date(filterDate);
      startOfDay.setHours(0, 0, 0, 0);
      const endOfDay = new Date(filterDate);
      endOfDay.setHours(23, 59, 59, 999);
      
      query.date = { $gte: startOfDay, $lte: endOfDay };
    }
    
    // Filter by session type
    if (sessionType) {
      query.sessionType = sessionType.toLowerCase();
    }
    
    const skip = (parseInt(page) - 1) * parseInt(limit);
    
    // Build sort object
    const sort = {};
    sort[sortBy] = sortOrder === 'desc' ? -1 : 1;
    
    const bookings = await Booking.find(query)
      .sort(sort)
      .limit(parseInt(limit))
      .skip(skip)
      .select('-__v');
    
    const total = await Booking.countDocuments(query);
    
    res.json({
      bookings,
      pagination: {
        current: parseInt(page),
        total: Math.ceil(total / parseInt(limit)),
        count: bookings.length,
        totalBookings: total
      }
    });
  } catch (error) {
    console.error('Bookings fetch error:', error);
    res.status(500).json({ error: 'Failed to fetch bookings' });
  }
});

// Get upcoming bookings
router.get('/upcoming', authenticateToken, async (req, res) => {
  try {
    const { limit = 10 } = req.query;
    
    const now = new Date();
    
    const bookings = await Booking.find({
      date: { $gte: now },
      status: { $in: ['pending', 'confirmed'] }
    })
      .sort({ date: 1, timeSlot: 1 })
      .limit(parseInt(limit))
      .select('-__v');
    
    res.json({ bookings });
  } catch (error) {
    console.error('Upcoming bookings fetch error:', error);
    res.status(500).json({ error: 'Failed to fetch upcoming bookings' });
  }
});

// Get booking statistics
router.get('/stats', authenticateToken, async (req, res) => {
  try {
    const now = new Date();
    const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
    const startOfYear = new Date(now.getFullYear(), 0, 1);
    
    const [
      totalBookings,
      thisMonthBookings,
      thisYearBookings,
      pendingBookings,
      confirmedBookings
    ] = await Promise.all([
      Booking.countDocuments(),
      Booking.countDocuments({ createdAt: { $gte: startOfMonth } }),
      Booking.countDocuments({ createdAt: { $gte: startOfYear } }),
      Booking.countDocuments({ status: 'pending' }),
      Booking.countDocuments({ status: 'confirmed', date: { $gte: now } })
    ]);
    
    res.json({
      total: totalBookings,
      thisMonth: thisMonthBookings,
      thisYear: thisYearBookings,
      pending: pendingBookings,
      upcomingConfirmed: confirmedBookings
    });
  } catch (error) {
    console.error('Booking stats error:', error);
    res.status(500).json({ error: 'Failed to fetch booking statistics' });
  }
});

export default router;
