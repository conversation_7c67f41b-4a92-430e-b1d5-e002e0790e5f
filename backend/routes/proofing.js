import express from 'express';
import ProofingGallery from '../models/ProofingGallery.js';
import Photo from '../models/Photo.js';
import { authenticateToken } from '../middleware/auth.js';

const router = express.Router();

// Validate proofing gallery password
router.post('/login', async (req, res) => {
  try {
    const { clientId, password } = req.body;
    
    if (!clientId || !password) {
      return res.status(400).json({ error: 'Client ID and password are required' });
    }
    
    const gallery = await ProofingGallery.findOne({ clientId });
    
    if (!gallery) {
      return res.status(404).json({ error: 'Gallery not found' });
    }
    
    if (!gallery.isAccessible()) {
      return res.status(403).json({ error: 'Gallery is no longer accessible' });
    }
    
    const isValidPassword = await gallery.comparePassword(password);
    
    if (!isValidPassword) {
      return res.status(401).json({ error: 'Invalid password' });
    }
    
    // Update access tracking
    gallery.accessCount += 1;
    gallery.lastAccessedAt = new Date();
    await gallery.save();
    
    res.json({ 
      success: true,
      message: 'Access granted',
      gallery: {
        clientId: gallery.clientId,
        title: gallery.title,
        description: gallery.description,
        expiresAt: gallery.expiresAt,
        settings: gallery.settings
      }
    });
  } catch (error) {
    console.error('Proofing login error:', error);
    res.status(500).json({ error: 'Login failed' });
  }
});

// Get proofing gallery photos (requires valid session)
router.get('/:clientId', async (req, res) => {
  try {
    const { clientId } = req.params;
    
    const gallery = await ProofingGallery.findOne({ clientId })
      .populate({
        path: 'photos.photo',
        select: 'title url filename metadata createdAt'
      });
    
    if (!gallery) {
      return res.status(404).json({ error: 'Gallery not found' });
    }
    
    if (!gallery.isAccessible()) {
      return res.status(403).json({ error: 'Gallery is no longer accessible' });
    }
    
    // Format photos for response
    const photos = gallery.photos.map(item => ({
      id: item.photo._id,
      title: item.photo.title,
      url: item.photo.url,
      filename: item.photo.filename,
      metadata: item.photo.metadata,
      createdAt: item.photo.createdAt,
      isSelected: item.isSelected,
      selectedAt: item.selectedAt
    }));
    
    res.json({
      gallery: {
        clientId: gallery.clientId,
        title: gallery.title,
        description: gallery.description,
        expiresAt: gallery.expiresAt,
        settings: gallery.settings,
        selectedCount: gallery.getSelectedCount()
      },
      photos
    });
  } catch (error) {
    console.error('Proofing gallery fetch error:', error);
    res.status(500).json({ error: 'Failed to fetch gallery' });
  }
});

// Toggle photo selection in proofing gallery
router.post('/:clientId/select', async (req, res) => {
  try {
    const { clientId } = req.params;
    const { photoId, selected } = req.body;
    
    if (!photoId || typeof selected !== 'boolean') {
      return res.status(400).json({ error: 'Photo ID and selection status are required' });
    }
    
    const gallery = await ProofingGallery.findOne({ clientId });
    
    if (!gallery) {
      return res.status(404).json({ error: 'Gallery not found' });
    }
    
    if (!gallery.isAccessible()) {
      return res.status(403).json({ error: 'Gallery is no longer accessible' });
    }
    
    // Find the photo in the gallery
    const photoIndex = gallery.photos.findIndex(p => p.photo.toString() === photoId);
    
    if (photoIndex === -1) {
      return res.status(404).json({ error: 'Photo not found in gallery' });
    }
    
    // Check selection limits
    if (selected && gallery.settings.maxSelections) {
      const currentSelections = gallery.getSelectedCount();
      if (currentSelections >= gallery.settings.maxSelections) {
        return res.status(400).json({ 
          error: 'Maximum selection limit reached',
          maxSelections: gallery.settings.maxSelections
        });
      }
    }
    
    // Update selection status
    gallery.photos[photoIndex].isSelected = selected;
    gallery.photos[photoIndex].selectedAt = selected ? new Date() : null;
    
    await gallery.save();
    
    res.json({
      success: true,
      photoId,
      selected,
      selectedCount: gallery.getSelectedCount()
    });
  } catch (error) {
    console.error('Photo selection error:', error);
    res.status(500).json({ error: 'Failed to update selection' });
  }
});

// Get selected photos for download
router.get('/:clientId/selected', async (req, res) => {
  try {
    const { clientId } = req.params;
    
    const gallery = await ProofingGallery.findOne({ clientId })
      .populate({
        path: 'photos.photo',
        select: 'title url filename originalName path metadata'
      });
    
    if (!gallery) {
      return res.status(404).json({ error: 'Gallery not found' });
    }
    
    if (!gallery.isAccessible()) {
      return res.status(403).json({ error: 'Gallery is no longer accessible' });
    }
    
    if (!gallery.settings.allowDownload) {
      return res.status(403).json({ error: 'Downloads are not allowed for this gallery' });
    }
    
    const selectedPhotos = gallery.photos
      .filter(item => item.isSelected)
      .map(item => ({
        id: item.photo._id,
        title: item.photo.title,
        url: item.photo.url,
        filename: item.photo.filename,
        originalName: item.photo.originalName,
        path: item.photo.path,
        metadata: item.photo.metadata,
        selectedAt: item.selectedAt
      }));
    
    // Update download count
    gallery.downloadCount += 1;
    await gallery.save();
    
    res.json({
      selectedPhotos,
      count: selectedPhotos.length
    });
  } catch (error) {
    console.error('Selected photos fetch error:', error);
    res.status(500).json({ error: 'Failed to fetch selected photos' });
  }
});

export default router;
