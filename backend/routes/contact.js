import express from 'express';
import Contact from '../models/Contact.js';
import { authenticateToken } from '../middleware/auth.js';
import rateLimit from 'express-rate-limit';

const router = express.Router();

// Rate limiting for contact form submissions
const contactLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 3, // limit each IP to 3 contact form submissions per windowMs
  message: 'Too many contact form submissions, please try again later.',
  standardHeaders: true,
  legacyHeaders: false,
});

// Submit contact form
router.post('/', contactLimiter, async (req, res) => {
  try {
    const { name, email, phone, subject, message, source } = req.body;
    
    // Validate required fields
    if (!name || !email || !message) {
      return res.status(400).json({ 
        error: 'Missing required fields',
        required: ['name', 'email', 'message']
      });
    }
    
    // Basic email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return res.status(400).json({ error: 'Invalid email format' });
    }
    
    // Check for spam
    const isSpam = Contact.detectSpam({ name, email, message });
    
    // Create contact submission
    const contact = new Contact({
      name: name.trim(),
      email: email.trim().toLowerCase(),
      phone: phone?.trim(),
      subject: subject?.trim(),
      message: message.trim(),
      source: source || 'website',
      ipAddress: req.ip,
      userAgent: req.get('User-Agent'),
      isSpam
    });
    
    await contact.save();
    
    // Don't reveal spam detection to user
    res.status(201).json({
      success: true,
      message: 'Thank you for your message. We will get back to you soon!'
    });
    
    // Log spam attempts
    if (isSpam) {
      console.log(`Potential spam contact from ${email}: ${message.substring(0, 100)}...`);
    }
  } catch (error) {
    console.error('Contact form submission error:', error);
    res.status(500).json({ error: 'Failed to submit contact form' });
  }
});

// Get all contact submissions (admin only)
router.get('/', authenticateToken, async (req, res) => {
  try {
    const { 
      status, 
      isSpam, 
      limit = 50, 
      page = 1,
      sortBy = 'createdAt',
      sortOrder = 'desc'
    } = req.query;
    
    const query = {};
    
    // Filter by status
    if (status) {
      query.status = status;
    }
    
    // Filter by spam status
    if (isSpam !== undefined) {
      query.isSpam = isSpam === 'true';
    }
    
    const skip = (parseInt(page) - 1) * parseInt(limit);
    
    // Build sort object
    const sort = {};
    sort[sortBy] = sortOrder === 'desc' ? -1 : 1;
    
    const contacts = await Contact.find(query)
      .sort(sort)
      .limit(parseInt(limit))
      .skip(skip)
      .populate('repliedBy', 'name email')
      .select('-__v');
    
    const total = await Contact.countDocuments(query);
    
    res.json({
      contacts,
      pagination: {
        current: parseInt(page),
        total: Math.ceil(total / parseInt(limit)),
        count: contacts.length,
        totalContacts: total
      }
    });
  } catch (error) {
    console.error('Contacts fetch error:', error);
    res.status(500).json({ error: 'Failed to fetch contacts' });
  }
});

// Get contact statistics
router.get('/stats', authenticateToken, async (req, res) => {
  try {
    const now = new Date();
    const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
    const startOfWeek = new Date(now.setDate(now.getDate() - now.getDay()));
    
    const [
      totalContacts,
      unreadCount,
      thisMonthContacts,
      thisWeekContacts,
      spamCount
    ] = await Promise.all([
      Contact.countDocuments({ isSpam: false }),
      Contact.getUnreadCount(),
      Contact.countDocuments({ 
        createdAt: { $gte: startOfMonth },
        isSpam: false 
      }),
      Contact.countDocuments({ 
        createdAt: { $gte: startOfWeek },
        isSpam: false 
      }),
      Contact.countDocuments({ isSpam: true })
    ]);
    
    res.json({
      total: totalContacts,
      unread: unreadCount,
      thisMonth: thisMonthContacts,
      thisWeek: thisWeekContacts,
      spam: spamCount
    });
  } catch (error) {
    console.error('Contact stats error:', error);
    res.status(500).json({ error: 'Failed to fetch contact statistics' });
  }
});

// Get single contact by ID
router.get('/:id', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;
    
    const contact = await Contact.findById(id)
      .populate('repliedBy', 'name email')
      .select('-__v');
    
    if (!contact) {
      return res.status(404).json({ error: 'Contact not found' });
    }
    
    // Mark as read if it's new
    if (contact.status === 'new') {
      await contact.markAsRead();
    }
    
    res.json({ contact });
  } catch (error) {
    console.error('Contact fetch error:', error);
    res.status(500).json({ error: 'Failed to fetch contact' });
  }
});

// Update contact status
router.patch('/:id/status', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;
    const { status, adminNotes } = req.body;
    
    const validStatuses = ['new', 'read', 'replied', 'archived'];
    
    if (!validStatuses.includes(status)) {
      return res.status(400).json({ error: 'Invalid status' });
    }
    
    const contact = await Contact.findById(id);
    
    if (!contact) {
      return res.status(404).json({ error: 'Contact not found' });
    }
    
    contact.status = status;
    
    if (adminNotes !== undefined) {
      contact.adminNotes = adminNotes.trim();
    }
    
    if (status === 'replied') {
      contact.repliedAt = new Date();
      contact.repliedBy = req.admin._id;
    }
    
    await contact.save();
    
    res.json({
      success: true,
      message: 'Contact status updated',
      contact: {
        id: contact._id,
        status: contact.status,
        repliedAt: contact.repliedAt
      }
    });
  } catch (error) {
    console.error('Contact status update error:', error);
    res.status(500).json({ error: 'Failed to update contact status' });
  }
});

// Mark contact as spam
router.patch('/:id/spam', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;
    const { isSpam } = req.body;
    
    const contact = await Contact.findById(id);
    
    if (!contact) {
      return res.status(404).json({ error: 'Contact not found' });
    }
    
    contact.isSpam = Boolean(isSpam);
    await contact.save();
    
    res.json({
      success: true,
      message: `Contact marked as ${isSpam ? 'spam' : 'not spam'}`,
      contact: {
        id: contact._id,
        isSpam: contact.isSpam
      }
    });
  } catch (error) {
    console.error('Contact spam update error:', error);
    res.status(500).json({ error: 'Failed to update spam status' });
  }
});

export default router;
