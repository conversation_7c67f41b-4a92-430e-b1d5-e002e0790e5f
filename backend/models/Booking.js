import mongoose from 'mongoose';

const bookingSchema = new mongoose.Schema({
  clientName: {
    type: String,
    required: true,
    trim: true
  },
  clientEmail: {
    type: String,
    required: true,
    trim: true,
    lowercase: true
  },
  clientPhone: {
    type: String,
    required: true,
    trim: true
  },
  date: {
    type: Date,
    required: true
  },
  timeSlot: {
    type: String,
    required: true,
    trim: true
  },
  sessionType: {
    type: String,
    required: true,
    enum: ['pregnancy', 'newborn', 'follow-up', 'baptism', 'communion', 'other'],
    lowercase: true
  },
  duration: {
    type: Number,
    default: 60 // minutes
  },
  location: {
    type: String,
    enum: ['studio', 'outdoor', 'client-home', 'venue'],
    default: 'studio'
  },
  status: {
    type: String,
    enum: ['pending', 'confirmed', 'cancelled', 'completed'],
    default: 'pending'
  },
  notes: {
    type: String,
    trim: true
  },
  adminNotes: {
    type: String,
    trim: true
  },
  price: {
    amount: {
      type: Number,
      min: 0
    },
    currency: {
      type: String,
      default: 'EUR'
    },
    isPaid: {
      type: Boolean,
      default: false
    }
  },
  reminderSent: {
    type: Boolean,
    default: false
  },
  confirmationSent: {
    type: Boolean,
    default: false
  }
}, {
  timestamps: true
});

// Index for efficient queries
bookingSchema.index({ date: 1, timeSlot: 1 });
bookingSchema.index({ clientEmail: 1 });
bookingSchema.index({ status: 1, date: 1 });
bookingSchema.index({ date: 1, status: 1 });

// Compound index to ensure no double bookings
bookingSchema.index({ date: 1, timeSlot: 1 }, { 
  unique: true,
  partialFilterExpression: { 
    status: { $in: ['pending', 'confirmed'] } 
  }
});

// Virtual for formatted date
bookingSchema.virtual('formattedDate').get(function() {
  return this.date.toLocaleDateString('en-GB', {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
});

// Virtual for formatted time
bookingSchema.virtual('formattedTime').get(function() {
  return this.timeSlot;
});

// Method to check if booking is upcoming
bookingSchema.methods.isUpcoming = function() {
  return this.date > new Date() && ['pending', 'confirmed'].includes(this.status);
};

// Method to check if booking can be cancelled
bookingSchema.methods.canBeCancelled = function() {
  const now = new Date();
  const bookingTime = new Date(this.date);
  const hoursUntilBooking = (bookingTime - now) / (1000 * 60 * 60);
  
  return hoursUntilBooking > 24 && ['pending', 'confirmed'].includes(this.status);
};

// Static method to get available time slots for a date
bookingSchema.statics.getAvailableTimeSlots = async function(date) {
  const defaultTimeSlots = [
    '09:00', '10:00', '11:00', '12:00', 
    '14:00', '15:00', '16:00', '17:00', '18:00'
  ];
  
  const startOfDay = new Date(date);
  startOfDay.setHours(0, 0, 0, 0);
  
  const endOfDay = new Date(date);
  endOfDay.setHours(23, 59, 59, 999);
  
  const bookedSlots = await this.find({
    date: { $gte: startOfDay, $lte: endOfDay },
    status: { $in: ['pending', 'confirmed'] }
  }).select('timeSlot');
  
  const bookedTimes = bookedSlots.map(booking => booking.timeSlot);
  
  return defaultTimeSlots.filter(slot => !bookedTimes.includes(slot));
};

// Ensure virtual fields are serialized
bookingSchema.set('toJSON', { virtuals: true });

export default mongoose.model('Booking', bookingSchema);
