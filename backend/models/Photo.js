import mongoose from 'mongoose';

const photoSchema = new mongoose.Schema({
  title: {
    type: String,
    required: true,
    trim: true
  },
  description: {
    type: String,
    trim: true
  },
  filename: {
    type: String,
    required: true
  },
  originalName: {
    type: String,
    required: true
  },
  path: {
    type: String,
    required: true
  },
  url: {
    type: String,
    required: true
  },
  category: {
    type: String,
    required: true,
    enum: ['pregnancy', 'newborns', 'follow-ups', 'baptisms', 'communions'],
    lowercase: true
  },
  tags: [{
    type: String,
    trim: true,
    lowercase: true
  }],
  isHighlighted: {
    type: Boolean,
    default: false
  },
  isPublic: {
    type: Boolean,
    default: true
  },
  metadata: {
    size: Number,
    mimetype: String,
    width: Number,
    height: Number
  },
  instagramId: {
    type: String,
    unique: true,
    sparse: true // Allows multiple null values
  },
  instagramUrl: String,
  sortOrder: {
    type: Number,
    default: 0
  }
}, {
  timestamps: true
});

// Index for efficient queries
photoSchema.index({ category: 1, createdAt: -1 });
photoSchema.index({ isHighlighted: 1, createdAt: -1 });
photoSchema.index({ isPublic: 1, category: 1, createdAt: -1 });

// Virtual for full URL
photoSchema.virtual('fullUrl').get(function() {
  if (this.instagramUrl) {
    return this.instagramUrl;
  }
  return `${process.env.BACKEND_URL || 'http://localhost:5000'}${this.url}`;
});

// Ensure virtual fields are serialized
photoSchema.set('toJSON', { virtuals: true });

export default mongoose.model('Photo', photoSchema);
