import mongoose from 'mongoose';
import bcrypt from 'bcryptjs';
import { v4 as uuidv4 } from 'uuid';

const proofingGallerySchema = new mongoose.Schema({
  clientId: {
    type: String,
    required: true,
    unique: true,
    default: () => uuidv4()
  },
  clientName: {
    type: String,
    required: true,
    trim: true
  },
  clientEmail: {
    type: String,
    required: true,
    trim: true,
    lowercase: true
  },
  password: {
    type: String,
    required: true
  },
  title: {
    type: String,
    required: true,
    trim: true
  },
  description: {
    type: String,
    trim: true
  },
  photos: [{
    photo: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Photo',
      required: true
    },
    isSelected: {
      type: Boolean,
      default: false
    },
    selectedAt: Date
  }],
  expiresAt: {
    type: Date,
    required: true,
    default: () => new Date(Date.now() + 30 * 24 * 60 * 60 * 1000) // 30 days from now
  },
  isActive: {
    type: Boolean,
    default: true
  },
  accessCount: {
    type: Number,
    default: 0
  },
  lastAccessedAt: Date,
  downloadCount: {
    type: Number,
    default: 0
  },
  settings: {
    allowDownload: {
      type: Boolean,
      default: true
    },
    maxSelections: {
      type: Number,
      default: null // null means unlimited
    },
    watermark: {
      type: Boolean,
      default: false
    }
  }
}, {
  timestamps: true
});

// Index for efficient queries
proofingGallerySchema.index({ clientId: 1 });
proofingGallerySchema.index({ expiresAt: 1 });
proofingGallerySchema.index({ isActive: 1, expiresAt: 1 });

// Hash password before saving
proofingGallerySchema.pre('save', async function(next) {
  if (!this.isModified('password')) return next();
  
  try {
    const salt = await bcrypt.genSalt(12);
    this.password = await bcrypt.hash(this.password, salt);
    next();
  } catch (error) {
    next(error);
  }
});

// Method to compare password
proofingGallerySchema.methods.comparePassword = async function(candidatePassword) {
  return bcrypt.compare(candidatePassword, this.password);
};

// Method to check if gallery is accessible
proofingGallerySchema.methods.isAccessible = function() {
  return this.isActive && this.expiresAt > new Date();
};

// Method to get selected photos count
proofingGallerySchema.methods.getSelectedCount = function() {
  return this.photos.filter(photo => photo.isSelected).length;
};

// Virtual for gallery URL
proofingGallerySchema.virtual('galleryUrl').get(function() {
  return `${process.env.FRONTEND_URL || 'http://localhost:5173'}/proofing-gallery/${this.clientId}`;
});

// Ensure virtual fields are serialized
proofingGallerySchema.set('toJSON', { virtuals: true });

export default mongoose.model('ProofingGallery', proofingGallerySchema);
