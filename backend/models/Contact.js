import mongoose from 'mongoose';

const contactSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
    trim: true
  },
  email: {
    type: String,
    required: true,
    trim: true,
    lowercase: true
  },
  phone: {
    type: String,
    trim: true
  },
  subject: {
    type: String,
    trim: true
  },
  message: {
    type: String,
    required: true,
    trim: true
  },
  status: {
    type: String,
    enum: ['new', 'read', 'replied', 'archived'],
    default: 'new'
  },
  source: {
    type: String,
    enum: ['website', 'instagram', 'referral', 'other'],
    default: 'website'
  },
  ipAddress: String,
  userAgent: String,
  isSpam: {
    type: Boolean,
    default: false
  },
  adminNotes: {
    type: String,
    trim: true
  },
  repliedAt: Date,
  repliedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Admin'
  }
}, {
  timestamps: true
});

// Index for efficient queries
contactSchema.index({ status: 1, createdAt: -1 });
contactSchema.index({ email: 1 });
contactSchema.index({ isSpam: 1, status: 1 });
contactSchema.index({ createdAt: -1 });

// Virtual for formatted date
contactSchema.virtual('formattedDate').get(function() {
  return this.createdAt.toLocaleDateString('en-GB', {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
});

// Method to mark as read
contactSchema.methods.markAsRead = function() {
  if (this.status === 'new') {
    this.status = 'read';
    return this.save();
  }
  return Promise.resolve(this);
};

// Method to mark as replied
contactSchema.methods.markAsReplied = function(adminId) {
  this.status = 'replied';
  this.repliedAt = new Date();
  this.repliedBy = adminId;
  return this.save();
};

// Static method to get unread count
contactSchema.statics.getUnreadCount = function() {
  return this.countDocuments({ status: 'new', isSpam: false });
};

// Static method to detect potential spam
contactSchema.statics.detectSpam = function(contactData) {
  const spamKeywords = [
    'seo', 'marketing', 'promotion', 'business opportunity',
    'make money', 'investment', 'loan', 'credit', 'casino',
    'viagra', 'pharmacy', 'weight loss'
  ];
  
  const text = `${contactData.name} ${contactData.email} ${contactData.message}`.toLowerCase();
  
  return spamKeywords.some(keyword => text.includes(keyword));
};

// Ensure virtual fields are serialized
contactSchema.set('toJSON', { virtuals: true });

export default mongoose.model('Contact', contactSchema);
