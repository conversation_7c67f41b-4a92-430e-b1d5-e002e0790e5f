{"name": "limarty-backend", "version": "1.0.0", "main": "server.js", "type": "module", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["photography", "portfolio", "booking"], "author": "LiMarty Photography", "license": "ISC", "description": "Backend API for LiMarty Photography Portfolio", "dependencies": {"bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^17.0.1", "express": "^5.1.0", "express-rate-limit": "^7.5.1", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.16.1", "multer": "^2.0.1", "nodemailer": "^7.0.4", "uuid": "^11.1.0"}, "devDependencies": {"nodemon": "^3.1.10"}}